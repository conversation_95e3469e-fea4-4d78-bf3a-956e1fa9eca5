import React, { useState, useEffect } from 'react';
import { User } from '../types';
import UserAvatar from './UserAvatar';
import { generateRandomNickname } from '../utils';

interface UserManagerProps {
  user: User | null;
  users: User[];
  onUserUpdate: (updates: Partial<User>) => void;
  className?: string;
}

const UserManager: React.FC<UserManagerProps> = ({
  user,
  users,
  onUserUpdate,
  className = '',
}) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingNickname, setEditingNickname] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);

  useEffect(() => {
    if (user) {
      setEditingNickname(user.nickname);
    }
  }, [user]);

  const handleSaveNickname = () => {
    if (editingNickname.trim() && user && editingNickname !== user.nickname) {
      onUserUpdate({ nickname: editingNickname.trim() });
    }
    setShowEditModal(false);
  };

  const handleGenerateRandomNickname = () => {
    const newNickname = generateRandomNickname();
    setEditingNickname(newNickname);
  };

  const handleResetToDefault = () => {
    if (user) {
      setEditingNickname(user.nickname);
    }
  };

  const getUserStatusText = () => {
    if (!user) return '未知';
    if (!user.isConnected) return '离线';
    if (user.isDeafened) return '已关闭声音';
    if (user.isMuted) return '已静音';
    if (user.isSpeaking) return '正在说话';
    return '在线';
  };

  const getUserStatusColor = () => {
    if (!user) return 'text-discord-gray-400';
    if (!user.isConnected) return 'text-discord-gray-400';
    if (user.isDeafened) return 'text-discord-red';
    if (user.isMuted) return 'text-discord-yellow';
    if (user.isSpeaking) return 'text-discord-green';
    return 'text-discord-green';
  };

  if (!user) {
    return <div className="text-gray-400 text-sm p-2">正在连接...</div>;
  }

  return (
    <div className={`${className}`}>
      {/* 在线用户列表 */}
      {users.length > 1 && (
        <div className="mb-4">
          <div className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2 px-2">
            在线用户 ({users.length})
          </div>
          <div className="space-y-1">
            {users.filter(u => u.id !== user?.id).map(onlineUser => (
              <div key={onlineUser.id} className="flex items-center space-x-2 px-2 py-1 rounded hover:bg-discord-gray-800">
                <UserAvatar
                  user={onlineUser}
                  size="small"
                  showStatus={true}
                  showVoiceActivity={true}
                />
                <div className="flex-1 min-w-0">
                  <div className="text-sm text-white truncate">
                    {onlineUser.nickname}
                  </div>
                  {onlineUser.isSpeaking && (
                    <div className="text-xs text-discord-green">
                      正在说话
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 当前用户信息显示 */}
      <div 
        className="flex items-center space-x-3 p-2 rounded hover:bg-discord-gray-800 cursor-pointer transition-colors duration-200"
        onClick={() => setShowUserMenu(!showUserMenu)}
      >
        <UserAvatar 
          user={user} 
          size="medium"
          showStatus={true}
          showVoiceActivity={true}
        />
        
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-white truncate">
            {user?.nickname}
          </div>
          <div className={`text-xs ${getUserStatusColor()}`}>
            {getUserStatusText()}
          </div>
        </div>
        
        {/* 设置图标 */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            setShowEditModal(true);
          }}
          className="w-6 h-6 flex items-center justify-center text-discord-gray-400 hover:text-gray-200 transition-colors duration-200"
          title="编辑用户信息"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* 用户菜单 */}
      {showUserMenu && (
        <div className="mt-2 bg-discord-gray-800 rounded-lg border border-discord-gray-600 overflow-hidden">
          <div className="p-3 border-b border-discord-gray-600">
            <div className="flex items-center space-x-3">
              <UserAvatar 
                user={user} 
                size="large"
                showStatus={true}
                showVoiceActivity={true}
              />
              <div>
                <div className="text-sm font-medium text-white">
                  {user?.nickname}
                </div>
                <div className={`text-xs ${getUserStatusColor()}`}>
                  {getUserStatusText()}
                </div>
              </div>
            </div>
          </div>
          
          <div className="p-2">
            <button
              onClick={() => {
                setShowEditModal(true);
                setShowUserMenu(false);
              }}
              className="w-full text-left px-3 py-2 text-sm text-white hover:bg-discord-gray-700 rounded transition-colors duration-200"
            >
              编辑昵称
            </button>
            <button
              onClick={() => {
                const newNickname = generateRandomNickname();
                onUserUpdate({ nickname: newNickname });
                setShowUserMenu(false);
              }}
              className="w-full text-left px-3 py-2 text-sm text-white hover:bg-discord-gray-700 rounded transition-colors duration-200"
            >
              随机昵称
            </button>
          </div>
        </div>
      )}

      {/* 编辑昵称模态框 */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-discord-gray-700 rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold text-white mb-4">编辑用户信息</h3>
            
            <div className="space-y-4">
              {/* 头像预览 */}
              <div className="flex justify-center">
                <UserAvatar 
                  user={{
                    ...user,
                    nickname: editingNickname,
                  }} 
                  size="large"
                  showStatus={true}
                  showVoiceActivity={false}
                />
              </div>
              
              {/* 昵称输入 */}
              <div>
                <label className="block text-sm font-medium text-discord-gray-300 mb-2">
                  昵称
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={editingNickname}
                    onChange={(e) => setEditingNickname(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleSaveNickname();
                      if (e.key === 'Escape') setShowEditModal(false);
                    }}
                    placeholder="输入昵称"
                    className="input-primary flex-1"
                    autoFocus
                    maxLength={32}
                  />
                  <button
                    onClick={handleGenerateRandomNickname}
                    className="px-3 py-2 bg-discord-gray-600 text-white rounded hover:bg-discord-gray-500 transition-colors duration-200"
                    title="生成随机昵称"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
                <div className="text-xs text-discord-gray-400 mt-1">
                  {editingNickname.length}/32 字符
                </div>
              </div>
              
              {/* 用户统计信息 */}
              <div className="bg-discord-gray-800 rounded p-3">
                <h4 className="text-sm font-medium text-white mb-2">用户信息</h4>
                <div className="space-y-1 text-xs text-discord-gray-300">
                  <div className="flex justify-between">
                    <span>加入时间:</span>
                    <span>{user?.joinedAt ? new Date(user.joinedAt).toLocaleDateString('zh-CN') : '-'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>连接状态:</span>
                    <span className={getUserStatusColor()}>
                      {getUserStatusText()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>音量设置:</span>
                    <span>{user?.volume}%</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={handleResetToDefault}
                className="px-4 py-2 bg-discord-gray-600 text-white rounded hover:bg-discord-gray-500 transition-colors duration-200"
              >
                重置
              </button>
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 bg-discord-gray-600 text-white rounded hover:bg-discord-gray-500 transition-colors duration-200"
              >
                取消
              </button>
              <button
                onClick={handleSaveNickname}
                className="px-4 py-2 bg-discord-blurple text-white rounded hover:bg-blue-600 transition-colors duration-200"
                disabled={!editingNickname.trim() || editingNickname.length > 32}
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* 点击外部关闭菜单 */}
      {showUserMenu && (
        <div 
          className="fixed inset-0 z-10" 
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </div>
  );
};

export default UserManager;
