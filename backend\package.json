{"name": "opz-backend", "version": "1.0.0", "description": "Cloudflare Workers backend for OPZ voice chat application", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:prod": "wrangler deploy --env production", "build": "wrangler build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["cloudflare", "workers", "voice", "chat", "webrtc"], "author": "", "license": "ISC", "devDependencies": {"@cloudflare/workers-types": "^4.20241218.0", "typescript": "^5.8.3", "wrangler": "^3.95.0"}, "dependencies": {"uuid": "^11.1.0"}}