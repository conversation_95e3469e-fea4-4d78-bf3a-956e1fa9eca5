import { 
  Room, 
  Channel, 
  User, 
  RoomEvent, 
  WebSocketMessage, 
  SignalingMessage,
  RoomStateData,
  CreateChannelRequest,
  UpdateChannelRequest,
  JoinChannelRequest
} from '../types';
import { 
  generateId, 
  formatTimestamp, 
  broadcastToConnections,
  createWebSocketResponse 
} from '../utils';

export class RoomState implements DurableObject {
  private room: Room;
  private connections: Map<string, WebSocket> = new Map();
  private connectionMetadata: Map<string, { userId: string }> = new Map();
  private lastActivity: string = formatTimestamp();

  constructor(private state: DurableObjectState, private env: any) {
    // 初始化默认房间
    this.room = {
      id: 'default-room',
      name: '语音聊天室',
      channels: [
        {
          id: generateId(),
          name: '大厅',
          type: 'voice',
          userCount: 0,
          users: [],
          createdAt: formatTimestamp(),
          updatedAt: formatTimestamp(),
        },
        {
          id: generateId(),
          name: '游戏频道',
          type: 'voice',
          userCount: 0,
          users: [],
          createdAt: formatTimestamp(),
          updatedAt: formatTimestamp(),
        }
      ],
      users: [],
      createdAt: formatTimestamp(),
      updatedAt: formatTimestamp(),
    };
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // WebSocket升级
    if (request.headers.get('Upgrade') === 'websocket') {
      return this.handleWebSocket(request);
    }

    // REST API路由
    switch (pathname) {
      case '/room':
        return this.handleGetRoom();
      case '/channels':
        if (request.method === 'POST') {
          return this.handleCreateChannel(request);
        }
        return this.handleGetChannels();
      case '/users':
        if (request.method === 'POST') {
          return this.handleCreateUser(request);
        }
        return this.handleGetUsers();
      default:
        if (pathname.startsWith('/channels/')) {
          const channelId = pathname.split('/')[2];
          if (request.method === 'PUT') {
            return this.handleUpdateChannel(channelId, request);
          } else if (request.method === 'DELETE') {
            return this.handleDeleteChannel(channelId);
          }
        } else if (pathname.startsWith('/users/')) {
          const userId = pathname.split('/')[2];
          if (request.method === 'PUT') {
            return this.handleUpdateUser(userId, request);
          } else if (request.method === 'DELETE') {
            return this.handleDeleteUser(userId);
          }
        }
        return new Response('Not Found', { status: 404 });
    }
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);

    const connectionId = generateId();
    this.connections.set(connectionId, server);

    server.accept();

    server.addEventListener('message', async (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data as string);
        await this.handleWebSocketMessage(connectionId, message);
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });

    server.addEventListener('close', () => {
      this.handleUserDisconnect(connectionId);
    });

    server.addEventListener('error', (error) => {
      console.error('WebSocket error:', error);
      this.handleUserDisconnect(connectionId);
    });

    this.updateLastActivity();
    return createWebSocketResponse(client);
  }

  private async handleWebSocketMessage(connectionId: string, message: WebSocketMessage): Promise<void> {
    this.updateLastActivity();

    switch (message.type) {
      case 'JOIN_ROOM':
        await this.handleJoinRoom(connectionId, message.payload);
        break;
      case 'LEAVE_ROOM':
        await this.handleLeaveRoom(connectionId, message.payload);
        break;
      case 'JOIN_CHANNEL':
        await this.handleJoinChannel(connectionId, message.payload);
        break;
      case 'LEAVE_CHANNEL':
        await this.handleLeaveChannel(connectionId, message.payload);
        break;
      case 'SIGNALING':
        await this.handleSignaling(connectionId, message.payload);
        break;
      case 'VOICE_STATE_UPDATE':
        await this.handleVoiceStateUpdate(connectionId, message.payload);
        break;
      case 'UPDATE_USER':
        await this.handleUserUpdate(connectionId, message.payload);
        break;
      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  private async handleGetRoom(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.room
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleCreateChannel(request: Request): Promise<Response> {
    const body: CreateChannelRequest = await request.json();
    const { name, type = 'voice' } = body;

    if (!name) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Channel name is required'
      }), { status: 400 });
    }

    const newChannel: Channel = {
      id: generateId(),
      name,
      type,
      userCount: 0,
      users: [],
      createdAt: formatTimestamp(),
      updatedAt: formatTimestamp(),
    };

    this.room.channels.push(newChannel);
    this.room.updatedAt = formatTimestamp();
    this.updateLastActivity();

    // 广播频道创建事件
    this.broadcastEvent({
      type: 'CHANNEL_CREATE',
      payload: newChannel
    });

    return new Response(JSON.stringify({
      success: true,
      data: newChannel
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleUpdateChannel(channelId: string, request: Request): Promise<Response> {
    const body: UpdateChannelRequest = await request.json();
    const { name } = body;

    const channelIndex = this.room.channels.findIndex(c => c.id === channelId);
    if (channelIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Channel not found'
      }), { status: 404 });
    }

    if (name) {
      this.room.channels[channelIndex].name = name;
      this.room.channels[channelIndex].updatedAt = formatTimestamp();
      this.room.updatedAt = formatTimestamp();
      this.updateLastActivity();

      // 广播频道更新事件
      this.broadcastEvent({
        type: 'CHANNEL_UPDATE',
        payload: this.room.channels[channelIndex]
      });
    }

    return new Response(JSON.stringify({
      success: true,
      data: this.room.channels[channelIndex]
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleDeleteChannel(channelId: string): Promise<Response> {
    const channelIndex = this.room.channels.findIndex(c => c.id === channelId);
    if (channelIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Channel not found'
      }), { status: 404 });
    }

    // 移除频道中的所有用户
    const channel = this.room.channels[channelIndex];
    for (const user of channel.users) {
      this.removeUserFromChannel(user.id, channelId);
    }

    this.room.channels.splice(channelIndex, 1);
    this.room.updatedAt = formatTimestamp();
    this.updateLastActivity();

    // 广播频道删除事件
    this.broadcastEvent({
      type: 'CHANNEL_DELETE',
      payload: { channelId }
    });

    return new Response(JSON.stringify({
      success: true,
      message: 'Channel deleted successfully'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private broadcastEvent(event: RoomEvent): void {
    console.log(`Broadcasting to ${this.connections.size} connections: ${event.type}`);
    
    // 确保USER_JOIN_CHANNEL和USER_LEAVE_CHANNEL事件的payload格式正确
    if (event.type === 'USER_JOIN_CHANNEL' && event.payload) {
      // 确保payload中包含完整的user对象
      const userId = event.payload.userId;
      const user = this.room.users.find(u => u.id === userId);
      if (user && !event.payload.user) {
        event.payload.user = user;
      }
    }
    
    const message: WebSocketMessage = {
      type: 'ROOM_EVENT',
      payload: event,
      timestamp: formatTimestamp()
    };
    
    broadcastToConnections(this.connections, message);
  }

  private updateLastActivity(): void {
    this.lastActivity = formatTimestamp();
  }

  private removeUserFromChannel(userId: string, channelId: string): void {
    const channel = this.room.channels.find(c => c.id === channelId);
    if (channel) {
      channel.users = channel.users.filter(u => u.id !== userId);
      channel.userCount = channel.users.length;
      channel.updatedAt = formatTimestamp();
    }
  }

  // 其他处理方法的占位符
  private async handleGetChannels(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.room.channels
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleCreateUser(request: Request): Promise<Response> {
    // 实现用户创建逻辑
    return new Response('Not implemented', { status: 501 });
  }

  private async handleGetUsers(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.room.users
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleUpdateUser(userId: string, request: Request): Promise<Response> {
    // 实现用户更新逻辑
    return new Response('Not implemented', { status: 501 });
  }

  private async handleDeleteUser(userId: string): Promise<Response> {
    // 实现用户删除逻辑
    return new Response('Not implemented', { status: 501 });
  }

  private async handleJoinRoom(connectionId: string, payload: any): Promise<void> {
    const { user } = payload;
    if (!user) return;

    // 存储连接与用户的映射关系
    this.connectionMetadata.set(connectionId, { userId: user.id });

    // 检查用户是否已经在房间中
    const existingUserIndex = this.room.users.findIndex(u => u.id === user.id);
    if (existingUserIndex >= 0) {
      // 更新现有用户
      this.room.users[existingUserIndex] = { ...user, isConnected: true };
    } else {
      // 添加新用户
      this.room.users.push({ ...user, isConnected: true });
    }

    this.room.updatedAt = formatTimestamp();
    this.updateLastActivity();

    // 广播用户加入事件
    this.broadcastEvent({
      type: 'USER_JOIN',
      payload: user
    });

    // 发送当前房间状态给新用户
    const ws = this.connections.get(connectionId);
    if (ws) {
      ws.send(JSON.stringify({
        type: 'ROOM_STATE',
        payload: this.room,
        timestamp: formatTimestamp()
      }));
    }

    console.log(`User ${user.nickname} joined room. Total users: ${this.room.users.length}`);
  }

  private async handleLeaveRoom(connectionId: string, payload: { userId: string }): Promise<void> {
    const { userId } = payload;
    if (!userId) return;

    this.handleUserDisconnect(connectionId, userId);
  }

  private handleUserDisconnect(connectionId: string, providedUserId?: string): void {
    const metadata = this.connectionMetadata.get(connectionId);
    const userId = providedUserId || metadata?.userId;

    if (!userId) {
      // 如果没有userId，可能是从未成功加入房间的连接，直接清理
      this.connections.delete(connectionId);
      return;
    }

    // 1. 从所有频道中移除用户
    this.room.channels.forEach(channel => {
      this.removeUserFromChannel(userId, channel.id);
    });

    // 2. 从房间用户列表中移除
    const userIndex = this.room.users.findIndex(u => u.id === userId);
    if (userIndex > -1) {
      this.room.users.splice(userIndex, 1);
    }
    
    // 3. 广播用户离开事件
    this.broadcastEvent({
      type: 'USER_LEAVE',
      payload: { userId }
    });

    // 4. 清理连接
    this.connections.delete(connectionId);
    this.connectionMetadata.delete(connectionId);
    this.updateLastActivity();
    
    console.log(`User ${userId} disconnected. Total users: ${this.room.users.length}`);
  }

  private async handleJoinChannel(connectionId: string, payload: JoinChannelRequest & { user: User }): Promise<void> {
    const { userId, channelId, user } = payload;
    if (!userId || !channelId) return;

    // 确保有完整的用户信息
    let userForChannel = user;
    if (!userForChannel) {
      // 如果没有提供用户信息，尝试从房间用户列表中获取
      const existingUser = this.room.users.find(u => u.id === userId);
      if (!existingUser) {
        console.error(`尝试加入频道的用户 ${userId} 不存在于房间中`);
        
        // 如果用户不存在于房间但提供了用户信息，尝试将其添加到房间
        if (user) {
          console.log(`尝试将用户 ${user.nickname} (${userId}) 自动添加到房间`);
          this.room.users.push({ ...user, isConnected: true });
          userForChannel = user;
          
          // 通知客户端用户已加入房间
          this.broadcastEvent({
            type: 'USER_JOIN',
            payload: userForChannel
          });
          
          console.log(`已自动将用户 ${user.nickname} 添加到房间。当前用户数: ${this.room.users.length}`);
        } else {
          // 无法自动添加用户，因为没有用户信息
          return;
        }
      } else {
        userForChannel = existingUser;
      }
    }

    const channel = this.room.channels.find(c => c.id === channelId);
    
    if (channel) {
      // 从用户之前所在的任何频道中移除
      this.room.channels.forEach(c => {
        this.removeUserFromChannel(userId, c.id);
      });

      // 将用户添加到新频道
      // 确保不会重复添加
      if (!channel.users.some(u => u.id === userId)) {
        channel.users.push(userForChannel);
      }
      channel.userCount = channel.users.length;
      channel.updatedAt = formatTimestamp();
      this.room.updatedAt = formatTimestamp();
      this.updateLastActivity();

      // 广播用户加入频道事件
      this.broadcastEvent({
        type: 'USER_JOIN_CHANNEL',
        payload: { userId, channelId, user: userForChannel }
      });

      console.log(`User ${userForChannel.nickname} joined channel ${channel.name}.`);
    } else {
      console.error(`频道 ${channelId} 不存在`);
    }
  }

  private async handleLeaveChannel(connectionId: string, payload: JoinChannelRequest): Promise<void> {
    const { userId, channelId } = payload;
    if (!userId || !channelId) return;

    this.removeUserFromChannel(userId, channelId);
    this.room.updatedAt = formatTimestamp();
    this.updateLastActivity();

    // 广播用户离开频道事件
    this.broadcastEvent({
      type: 'USER_LEAVE_CHANNEL',
      payload: { userId, channelId }
    });

    const user = this.room.users.find(u => u.id === userId);
    const channel = this.room.channels.find(c => c.id === channelId);
    if (user && channel) {
      console.log(`User ${user.nickname} left channel ${channel.name}.`);
    }
  }

  private async handleSignaling(connectionId: string, payload: SignalingMessage): Promise<void> {
    // 当前实现：将信令广播给房间中的每个人。
    // 前端将负责根据channelId和recipientId过滤事件。
    // TODO: 优化为只发送给频道内的目标用户，这需要建立 connectionId 和 userId 之间的映射。
    
    this.broadcastEvent({
      type: 'SIGNALING',
      payload
    });
  }

  private async handleVoiceStateUpdate(connectionId: string, payload: any): Promise<void> {
    const { userId, channelId, isMuted, isDeafened, isSpeaking } = payload;
    // ... existing code ...
  }

  private async handleUserUpdate(connectionId: string, payload: { userId: string, updates: Partial<User> }): Promise<void> {
    const { userId, updates } = payload;
    const userIndex = this.room.users.findIndex(u => u.id === userId);

    if (userIndex !== -1) {
      const updatedUser = { ...this.room.users[userIndex], ...updates };
      this.room.users[userIndex] = updatedUser;
      
      this.room.updatedAt = formatTimestamp();
      this.updateLastActivity();

      // 在所有频道中也更新该用户的信息
      this.room.channels.forEach(channel => {
        const userInChannelIndex = channel.users.findIndex(u => u.id === userId);
        if (userInChannelIndex !== -1) {
          channel.users[userInChannelIndex] = { ...channel.users[userInChannelIndex], ...updates };
        }
      });
      
      console.log(`User ${updatedUser.nickname} (${userId}) updated state.`);

      // 广播用户更新事件
      this.broadcastEvent({
        type: 'USER_UPDATE',
        payload: updatedUser
      });
    } else {
      console.warn(`Attempted to update non-existent user: ${userId}`);
    }
  }
}
