import { 
  Room, 
  Channel, 
  User, 
  RoomEvent, 
  WebSocketMessage, 
  SignalingMessage,
  RoomStateData 
} from '../types';
import { 
  generateId, 
  formatTimestamp, 
  broadcastToConnections,
  createWebSocketResponse 
} from '../utils';

export class RoomState implements DurableObject {
  private room: Room;
  private connections: Map<string, WebSocket> = new Map();
  private lastActivity: string = formatTimestamp();

  constructor(private state: DurableObjectState, private env: any) {
    // 初始化默认房间
    this.room = {
      id: 'default-room',
      name: '语音聊天室',
      channels: [
        {
          id: generateId(),
          name: '大厅',
          type: 'voice',
          userCount: 0,
          users: [],
          createdAt: formatTimestamp(),
          updatedAt: formatTimestamp(),
        },
        {
          id: generateId(),
          name: '游戏频道',
          type: 'voice',
          userCount: 0,
          users: [],
          createdAt: formatTimestamp(),
          updatedAt: formatTimestamp(),
        }
      ],
      users: [],
      createdAt: formatTimestamp(),
      updatedAt: formatTimestamp(),
    };
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // WebSocket升级
    if (request.headers.get('Upgrade') === 'websocket') {
      return this.handleWebSocket(request);
    }

    // REST API路由
    switch (pathname) {
      case '/room':
        return this.handleGetRoom();
      case '/channels':
        if (request.method === 'POST') {
          return this.handleCreateChannel(request);
        }
        return this.handleGetChannels();
      case '/users':
        if (request.method === 'POST') {
          return this.handleCreateUser(request);
        }
        return this.handleGetUsers();
      default:
        if (pathname.startsWith('/channels/')) {
          const channelId = pathname.split('/')[2];
          if (request.method === 'PUT') {
            return this.handleUpdateChannel(channelId, request);
          } else if (request.method === 'DELETE') {
            return this.handleDeleteChannel(channelId);
          }
        } else if (pathname.startsWith('/users/')) {
          const userId = pathname.split('/')[2];
          if (request.method === 'PUT') {
            return this.handleUpdateUser(userId, request);
          } else if (request.method === 'DELETE') {
            return this.handleDeleteUser(userId);
          }
        }
        return new Response('Not Found', { status: 404 });
    }
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);

    const connectionId = generateId();
    this.connections.set(connectionId, server);

    server.accept();

    server.addEventListener('message', async (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data as string);
        await this.handleWebSocketMessage(connectionId, message);
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });

    server.addEventListener('close', () => {
      this.connections.delete(connectionId);
      this.updateLastActivity();
    });

    server.addEventListener('error', (error) => {
      console.error('WebSocket error:', error);
      this.connections.delete(connectionId);
    });

    this.updateLastActivity();
    return createWebSocketResponse(client);
  }

  private async handleWebSocketMessage(connectionId: string, message: WebSocketMessage): Promise<void> {
    this.updateLastActivity();

    switch (message.type) {
      case 'JOIN_ROOM':
        await this.handleJoinRoom(connectionId, message.payload);
        break;
      case 'LEAVE_ROOM':
        await this.handleLeaveRoom(connectionId, message.payload);
        break;
      case 'JOIN_CHANNEL':
        await this.handleJoinChannel(connectionId, message.payload);
        break;
      case 'LEAVE_CHANNEL':
        await this.handleLeaveChannel(connectionId, message.payload);
        break;
      case 'SIGNALING':
        await this.handleSignaling(connectionId, message.payload);
        break;
      case 'VOICE_STATE_UPDATE':
        await this.handleVoiceStateUpdate(connectionId, message.payload);
        break;
      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  private async handleGetRoom(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.room
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleCreateChannel(request: Request): Promise<Response> {
    const body = await request.json();
    const { name, type = 'voice' } = body;

    if (!name) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Channel name is required'
      }), { status: 400 });
    }

    const newChannel: Channel = {
      id: generateId(),
      name,
      type,
      userCount: 0,
      users: [],
      createdAt: formatTimestamp(),
      updatedAt: formatTimestamp(),
    };

    this.room.channels.push(newChannel);
    this.room.updatedAt = formatTimestamp();
    this.updateLastActivity();

    // 广播频道创建事件
    this.broadcastEvent({
      type: 'CHANNEL_CREATE',
      payload: newChannel
    });

    return new Response(JSON.stringify({
      success: true,
      data: newChannel
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleUpdateChannel(channelId: string, request: Request): Promise<Response> {
    const body = await request.json();
    const { name } = body;

    const channelIndex = this.room.channels.findIndex(c => c.id === channelId);
    if (channelIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Channel not found'
      }), { status: 404 });
    }

    if (name) {
      this.room.channels[channelIndex].name = name;
      this.room.channels[channelIndex].updatedAt = formatTimestamp();
      this.room.updatedAt = formatTimestamp();
      this.updateLastActivity();

      // 广播频道更新事件
      this.broadcastEvent({
        type: 'CHANNEL_UPDATE',
        payload: this.room.channels[channelIndex]
      });
    }

    return new Response(JSON.stringify({
      success: true,
      data: this.room.channels[channelIndex]
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleDeleteChannel(channelId: string): Promise<Response> {
    const channelIndex = this.room.channels.findIndex(c => c.id === channelId);
    if (channelIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Channel not found'
      }), { status: 404 });
    }

    // 移除频道中的所有用户
    const channel = this.room.channels[channelIndex];
    for (const user of channel.users) {
      this.removeUserFromChannel(user.id, channelId);
    }

    this.room.channels.splice(channelIndex, 1);
    this.room.updatedAt = formatTimestamp();
    this.updateLastActivity();

    // 广播频道删除事件
    this.broadcastEvent({
      type: 'CHANNEL_DELETE',
      payload: { channelId }
    });

    return new Response(JSON.stringify({
      success: true,
      message: 'Channel deleted successfully'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private broadcastEvent(event: RoomEvent): void {
    const message = {
      type: 'ROOM_EVENT',
      payload: event,
      timestamp: formatTimestamp()
    };

    broadcastToConnections(this.connections, message);
  }

  private updateLastActivity(): void {
    this.lastActivity = formatTimestamp();
  }

  private removeUserFromChannel(userId: string, channelId: string): void {
    const channel = this.room.channels.find(c => c.id === channelId);
    if (channel) {
      channel.users = channel.users.filter(u => u.id !== userId);
      channel.userCount = channel.users.length;
      channel.updatedAt = formatTimestamp();
    }
  }

  // 其他处理方法的占位符
  private async handleGetChannels(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.room.channels
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleCreateUser(request: Request): Promise<Response> {
    // 实现用户创建逻辑
    return new Response('Not implemented', { status: 501 });
  }

  private async handleGetUsers(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.room.users
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleUpdateUser(userId: string, request: Request): Promise<Response> {
    // 实现用户更新逻辑
    return new Response('Not implemented', { status: 501 });
  }

  private async handleDeleteUser(userId: string): Promise<Response> {
    // 实现用户删除逻辑
    return new Response('Not implemented', { status: 501 });
  }

  private async handleJoinRoom(connectionId: string, payload: any): Promise<void> {
    // 实现加入房间逻辑
  }

  private async handleLeaveRoom(connectionId: string, payload: any): Promise<void> {
    // 实现离开房间逻辑
  }

  private async handleJoinChannel(connectionId: string, payload: any): Promise<void> {
    // 实现加入频道逻辑
  }

  private async handleLeaveChannel(connectionId: string, payload: any): Promise<void> {
    // 实现离开频道逻辑
  }

  private async handleSignaling(connectionId: string, payload: SignalingMessage): Promise<void> {
    // 实现WebRTC信令逻辑
    this.broadcastEvent({
      type: 'SIGNALING',
      payload
    });
  }

  private async handleVoiceStateUpdate(connectionId: string, payload: any): Promise<void> {
    // 实现语音状态更新逻辑
  }
}
