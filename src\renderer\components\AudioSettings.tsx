import React, { useState, useEffect } from 'react';
import { AudioDeviceManager } from '../utils/audioProcessor';
import AudioVisualizer from './AudioVisualizer';

interface AudioSettingsProps {
  deviceManager: AudioDeviceManager;
  currentVolume: number;
  onVolumeChange: (volume: number) => void;
  onClose: () => void;
  className?: string;
}

const AudioSettings: React.FC<AudioSettingsProps> = ({
  deviceManager,
  currentVolume,
  onVolumeChange,
  onClose,
  className = '',
}) => {
  const [audioInputs, setAudioInputs] = useState<MediaDeviceInfo[]>([]);
  const [audioOutputs, setAudioOutputs] = useState<MediaDeviceInfo[]>([]);
  const [selectedInput, setSelectedInput] = useState<string>('');
  const [selectedOutput, setSelectedOutput] = useState<string>('');
  const [inputVolume, setInputVolume] = useState(currentVolume);
  const [outputVolume, setOutputVolume] = useState(80);
  const [noiseReduction, setNoiseReduction] = useState(true);
  const [echoCancellation, setEchoCancellation] = useState(true);
  const [autoGainControl, setAutoGainControl] = useState(true);
  const [isTestingMic, setIsTestingMic] = useState(false);

  useEffect(() => {
    loadDevices();
  }, []);

  const loadDevices = async () => {
    try {
      const devices = await deviceManager.getDevices();
      setAudioInputs(devices.audioInputs);
      setAudioOutputs(devices.audioOutputs);
      
      // 设置默认设备
      if (devices.audioInputs.length > 0) {
        setSelectedInput(devices.audioInputs[0].deviceId);
      }
      if (devices.audioOutputs.length > 0) {
        setSelectedOutput(devices.audioOutputs[0].deviceId);
      }
    } catch (error) {
      console.error('Failed to load audio devices:', error);
    }
  };

  const handleInputVolumeChange = (volume: number) => {
    setInputVolume(volume);
    onVolumeChange(volume);
  };

  const handleTestMicrophone = async () => {
    setIsTestingMic(!isTestingMic);
    // 这里可以实现麦克风测试逻辑
  };

  const ToggleSwitch: React.FC<{
    checked: boolean;
    onChange: (checked: boolean) => void;
    label: string;
  }> = ({ checked, onChange, label }) => (
    <div className="flex items-center justify-between">
      <span className="text-sm text-discord-gray-300">{label}</span>
      <button
        onClick={() => onChange(!checked)}
        className={`w-10 h-6 rounded-full relative transition-colors duration-200 ${
          checked ? 'bg-discord-blurple' : 'bg-discord-gray-600'
        }`}
      >
        <div
          className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform duration-200 ${
            checked ? 'transform translate-x-5' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );

  return (
    <div className={`bg-discord-gray-800 rounded-lg shadow-lg border border-discord-gray-600 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">音频设置</h3>
        <button
          onClick={onClose}
          className="w-6 h-6 flex items-center justify-center text-discord-gray-400 hover:text-gray-200 transition-colors duration-200"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      <div className="space-y-6">
        {/* 输入设备 */}
        <div>
          <label className="block text-sm font-medium text-discord-gray-300 mb-2">
            输入设备
          </label>
          <select
            value={selectedInput}
            onChange={(e) => setSelectedInput(e.target.value)}
            className="w-full bg-discord-gray-700 border border-discord-gray-600 text-white rounded px-3 py-2 focus:outline-none focus:border-discord-blurple"
          >
            {audioInputs.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `麦克风 ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
        </div>

        {/* 输出设备 */}
        <div>
          <label className="block text-sm font-medium text-discord-gray-300 mb-2">
            输出设备
          </label>
          <select
            value={selectedOutput}
            onChange={(e) => setSelectedOutput(e.target.value)}
            className="w-full bg-discord-gray-700 border border-discord-gray-600 text-white rounded px-3 py-2 focus:outline-none focus:border-discord-blurple"
          >
            {audioOutputs.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `扬声器 ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
        </div>

        {/* 输入音量 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-discord-gray-300">
              输入音量: {inputVolume}%
            </label>
            <button
              onClick={handleTestMicrophone}
              className={`px-3 py-1 text-xs rounded transition-colors duration-200 ${
                isTestingMic
                  ? 'bg-discord-red text-white'
                  : 'bg-discord-gray-600 text-discord-gray-300 hover:bg-discord-gray-500'
              }`}
            >
              {isTestingMic ? '停止测试' : '测试麦克风'}
            </button>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={inputVolume}
            onChange={(e) => handleInputVolumeChange(parseInt(e.target.value))}
            className="w-full h-2 bg-discord-gray-700 rounded-lg appearance-none cursor-pointer slider"
          />
          
          {/* 音频可视化 */}
          <div className="mt-2 flex items-center space-x-2">
            <span className="text-xs text-discord-gray-400">音量:</span>
            <AudioVisualizer
              isActive={isTestingMic}
              width={120}
              height={16}
              barCount={10}
              color="#57f287"
            />
          </div>
        </div>

        {/* 输出音量 */}
        <div>
          <label className="block text-sm font-medium text-discord-gray-300 mb-2">
            输出音量: {outputVolume}%
          </label>
          <input
            type="range"
            min="0"
            max="100"
            value={outputVolume}
            onChange={(e) => setOutputVolume(parseInt(e.target.value))}
            className="w-full h-2 bg-discord-gray-700 rounded-lg appearance-none cursor-pointer slider"
          />
        </div>

        {/* 音频处理选项 */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-discord-gray-300">音频处理</h4>
          
          <ToggleSwitch
            checked={noiseReduction}
            onChange={setNoiseReduction}
            label="噪音抑制"
          />
          
          <ToggleSwitch
            checked={echoCancellation}
            onChange={setEchoCancellation}
            label="回声消除"
          />
          
          <ToggleSwitch
            checked={autoGainControl}
            onChange={setAutoGainControl}
            label="自动增益控制"
          />
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-discord-gray-600">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-discord-gray-600 text-white rounded hover:bg-discord-gray-500 transition-colors duration-200"
          >
            取消
          </button>
          <button
            onClick={() => {
              // 保存设置
              onClose();
            }}
            className="px-4 py-2 bg-discord-blurple text-white rounded hover:bg-blue-600 transition-colors duration-200"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};

export default AudioSettings;
