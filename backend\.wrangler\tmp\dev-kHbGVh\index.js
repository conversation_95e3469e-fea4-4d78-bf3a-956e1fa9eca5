var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-uSIFVA/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// src/utils/index.ts
function generateId() {
  return crypto.randomUUID();
}
__name(generateId, "generateId");
function createSuccessResponse(data, message) {
  return {
    success: true,
    data,
    message
  };
}
__name(createSuccessResponse, "createSuccessResponse");
function createErrorResponse(error, message) {
  return {
    success: false,
    error,
    message
  };
}
__name(createErrorResponse, "createErrorResponse");
function createJsonResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization"
    }
  });
}
__name(createJsonResponse, "createJsonResponse");
function handleCorsOptions() {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400"
    }
  });
}
__name(handleCorsOptions, "handleCorsOptions");
function formatTimestamp(date = /* @__PURE__ */ new Date()) {
  return date.toISOString();
}
__name(formatTimestamp, "formatTimestamp");
function broadcastToConnections(connections, message, excludeId) {
  const messageStr = JSON.stringify(message);
  for (const [connectionId, ws] of connections.entries()) {
    if (excludeId && connectionId === excludeId) {
      continue;
    }
    try {
      if (ws.readyState === WebSocket.READY_STATE_OPEN) {
        ws.send(messageStr);
      } else {
        connections.delete(connectionId);
      }
    } catch (error) {
      console.error(`Failed to send message to connection ${connectionId}:`, error);
      connections.delete(connectionId);
    }
  }
}
__name(broadcastToConnections, "broadcastToConnections");
function createWebSocketResponse(webSocket) {
  return new Response(null, {
    status: 101,
    webSocket
  });
}
__name(createWebSocketResponse, "createWebSocketResponse");
var RateLimiter = class {
  constructor(maxRequests = 100, windowMs = 6e4) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }
  static {
    __name(this, "RateLimiter");
  }
  requests = /* @__PURE__ */ new Map();
  isAllowed(identifier) {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    let requests = this.requests.get(identifier) || [];
    requests = requests.filter((timestamp) => timestamp > windowStart);
    if (requests.length >= this.maxRequests) {
      return false;
    }
    requests.push(now);
    this.requests.set(identifier, requests);
    return true;
  }
  // 清理过期数据
  cleanup() {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    for (const [identifier, requests] of this.requests.entries()) {
      const validRequests = requests.filter((timestamp) => timestamp > windowStart);
      if (validRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, validRequests);
      }
    }
  }
};

// src/handlers/RoomState.ts
var RoomState = class {
  constructor(state, env) {
    this.state = state;
    this.env = env;
    this.room = {
      id: "default-room",
      name: "\u8BED\u97F3\u804A\u5929\u5BA4",
      channels: [
        {
          id: generateId(),
          name: "\u5927\u5385",
          type: "voice",
          userCount: 0,
          users: [],
          createdAt: formatTimestamp(),
          updatedAt: formatTimestamp()
        },
        {
          id: generateId(),
          name: "\u6E38\u620F\u9891\u9053",
          type: "voice",
          userCount: 0,
          users: [],
          createdAt: formatTimestamp(),
          updatedAt: formatTimestamp()
        }
      ],
      users: [],
      createdAt: formatTimestamp(),
      updatedAt: formatTimestamp()
    };
  }
  static {
    __name(this, "RoomState");
  }
  room;
  connections = /* @__PURE__ */ new Map();
  lastActivity = formatTimestamp();
  async fetch(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    if (request.headers.get("Upgrade") === "websocket") {
      return this.handleWebSocket(request);
    }
    switch (pathname) {
      case "/room":
        return this.handleGetRoom();
      case "/channels":
        if (request.method === "POST") {
          return this.handleCreateChannel(request);
        }
        return this.handleGetChannels();
      case "/users":
        if (request.method === "POST") {
          return this.handleCreateUser(request);
        }
        return this.handleGetUsers();
      default:
        if (pathname.startsWith("/channels/")) {
          const channelId = pathname.split("/")[2];
          if (request.method === "PUT") {
            return this.handleUpdateChannel(channelId, request);
          } else if (request.method === "DELETE") {
            return this.handleDeleteChannel(channelId);
          }
        } else if (pathname.startsWith("/users/")) {
          const userId = pathname.split("/")[2];
          if (request.method === "PUT") {
            return this.handleUpdateUser(userId, request);
          } else if (request.method === "DELETE") {
            return this.handleDeleteUser(userId);
          }
        }
        return new Response("Not Found", { status: 404 });
    }
  }
  async handleWebSocket(request) {
    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);
    const connectionId = generateId();
    this.connections.set(connectionId, server);
    server.accept();
    server.addEventListener("message", async (event) => {
      try {
        const message = JSON.parse(event.data);
        await this.handleWebSocketMessage(connectionId, message);
      } catch (error) {
        console.error("WebSocket message error:", error);
      }
    });
    server.addEventListener("close", () => {
      this.connections.delete(connectionId);
      this.updateLastActivity();
    });
    server.addEventListener("error", (error) => {
      console.error("WebSocket error:", error);
      this.connections.delete(connectionId);
    });
    this.updateLastActivity();
    return createWebSocketResponse(client);
  }
  async handleWebSocketMessage(connectionId, message) {
    this.updateLastActivity();
    switch (message.type) {
      case "JOIN_ROOM":
        await this.handleJoinRoom(connectionId, message.payload);
        break;
      case "LEAVE_ROOM":
        await this.handleLeaveRoom(connectionId, message.payload);
        break;
      case "JOIN_CHANNEL":
        await this.handleJoinChannel(connectionId, message.payload);
        break;
      case "LEAVE_CHANNEL":
        await this.handleLeaveChannel(connectionId, message.payload);
        break;
      case "SIGNALING":
        await this.handleSignaling(connectionId, message.payload);
        break;
      case "VOICE_STATE_UPDATE":
        await this.handleVoiceStateUpdate(connectionId, message.payload);
        break;
      default:
        console.warn("Unknown message type:", message.type);
    }
  }
  async handleGetRoom() {
    return new Response(JSON.stringify({
      success: true,
      data: this.room
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  async handleCreateChannel(request) {
    const body = await request.json();
    const { name, type = "voice" } = body;
    if (!name) {
      return new Response(JSON.stringify({
        success: false,
        error: "Channel name is required"
      }), { status: 400 });
    }
    const newChannel = {
      id: generateId(),
      name,
      type,
      userCount: 0,
      users: [],
      createdAt: formatTimestamp(),
      updatedAt: formatTimestamp()
    };
    this.room.channels.push(newChannel);
    this.room.updatedAt = formatTimestamp();
    this.updateLastActivity();
    this.broadcastEvent({
      type: "CHANNEL_CREATE",
      payload: newChannel
    });
    return new Response(JSON.stringify({
      success: true,
      data: newChannel
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  async handleUpdateChannel(channelId, request) {
    const body = await request.json();
    const { name } = body;
    const channelIndex = this.room.channels.findIndex((c) => c.id === channelId);
    if (channelIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Channel not found"
      }), { status: 404 });
    }
    if (name) {
      this.room.channels[channelIndex].name = name;
      this.room.channels[channelIndex].updatedAt = formatTimestamp();
      this.room.updatedAt = formatTimestamp();
      this.updateLastActivity();
      this.broadcastEvent({
        type: "CHANNEL_UPDATE",
        payload: this.room.channels[channelIndex]
      });
    }
    return new Response(JSON.stringify({
      success: true,
      data: this.room.channels[channelIndex]
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  async handleDeleteChannel(channelId) {
    const channelIndex = this.room.channels.findIndex((c) => c.id === channelId);
    if (channelIndex === -1) {
      return new Response(JSON.stringify({
        success: false,
        error: "Channel not found"
      }), { status: 404 });
    }
    const channel = this.room.channels[channelIndex];
    for (const user of channel.users) {
      this.removeUserFromChannel(user.id, channelId);
    }
    this.room.channels.splice(channelIndex, 1);
    this.room.updatedAt = formatTimestamp();
    this.updateLastActivity();
    this.broadcastEvent({
      type: "CHANNEL_DELETE",
      payload: { channelId }
    });
    return new Response(JSON.stringify({
      success: true,
      message: "Channel deleted successfully"
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  broadcastEvent(event) {
    const message = {
      type: "ROOM_EVENT",
      payload: event,
      timestamp: formatTimestamp()
    };
    broadcastToConnections(this.connections, message);
  }
  updateLastActivity() {
    this.lastActivity = formatTimestamp();
  }
  removeUserFromChannel(userId, channelId) {
    const channel = this.room.channels.find((c) => c.id === channelId);
    if (channel) {
      channel.users = channel.users.filter((u) => u.id !== userId);
      channel.userCount = channel.users.length;
      channel.updatedAt = formatTimestamp();
    }
  }
  // 其他处理方法的占位符
  async handleGetChannels() {
    return new Response(JSON.stringify({
      success: true,
      data: this.room.channels
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  async handleCreateUser(request) {
    return new Response("Not implemented", { status: 501 });
  }
  async handleGetUsers() {
    return new Response(JSON.stringify({
      success: true,
      data: this.room.users
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  async handleUpdateUser(userId, request) {
    return new Response("Not implemented", { status: 501 });
  }
  async handleDeleteUser(userId) {
    return new Response("Not implemented", { status: 501 });
  }
  async handleJoinRoom(connectionId, payload) {
  }
  async handleLeaveRoom(connectionId, payload) {
  }
  async handleJoinChannel(connectionId, payload) {
  }
  async handleLeaveChannel(connectionId, payload) {
  }
  async handleSignaling(connectionId, payload) {
    this.broadcastEvent({
      type: "SIGNALING",
      payload
    });
  }
  async handleVoiceStateUpdate(connectionId, payload) {
  }
};

// src/handlers/UserState.ts
var UserState = class {
  constructor(state, env) {
    this.state = state;
    this.env = env;
  }
  static {
    __name(this, "UserState");
  }
  user = null;
  webSocket = null;
  async fetch(request) {
    if (request.headers.get("Upgrade") !== "websocket") {
      return new Response("Expected a WebSocket connection", { status: 400 });
    }
    const pair = new WebSocketPair();
    const [client, server] = Object.values(pair);
    server.accept();
    this.webSocket = server;
    server.addEventListener("message", (event) => {
      console.log("UserState received message:", event.data);
    });
    server.addEventListener("close", () => {
      this.webSocket = null;
    });
    return new Response(null, { status: 101, webSocket: client });
  }
  async setUser(user) {
    this.user = user;
    await this.state.storage.put("user", this.user);
  }
};

// src/handlers/CallsHandler.ts
var CallsHandler = class {
  constructor(env) {
    this.env = env;
  }
  static {
    __name(this, "CallsHandler");
  }
  baseUrl = "https://rtc.live.cloudflare.com/v1";
  // 创建新的WebRTC会话
  async createSession(correlationId) {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/new`;
      const params = new URLSearchParams();
      if (correlationId) {
        params.append("correlationId", correlationId);
      }
      const response = await fetch(`${url}?${params.toString()}`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          "Content-Type": "application/json"
        }
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Calls API error:", errorText);
        return createJsonResponse(
          createErrorResponse("Failed to create session", errorText),
          response.status
        );
      }
      const sessionData = await response.json();
      return createJsonResponse(createSuccessResponse(sessionData));
    } catch (error) {
      console.error("Create session error:", error);
      return createJsonResponse(
        createErrorResponse("Internal server error", error instanceof Error ? error.message : "Unknown error"),
        500
      );
    }
  }
  // 添加音轨到会话
  async addTracks(sessionId, tracks, sessionDescription) {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/new`;
      const requestBody = {
        tracks
      };
      if (sessionDescription) {
        requestBody.sessionDescription = sessionDescription;
      }
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Add tracks error:", errorText);
        return createJsonResponse(
          createErrorResponse("Failed to add tracks", errorText),
          response.status
        );
      }
      const tracksData = await response.json();
      return createJsonResponse(createSuccessResponse(tracksData));
    } catch (error) {
      console.error("Add tracks error:", error);
      return createJsonResponse(
        createErrorResponse("Internal server error", error instanceof Error ? error.message : "Unknown error"),
        500
      );
    }
  }
  // 重新协商WebRTC会话
  async renegotiate(sessionId, sessionDescription) {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/renegotiate`;
      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Authorization": `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ sessionDescription })
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Renegotiate error:", errorText);
        return createJsonResponse(
          createErrorResponse("Failed to renegotiate", errorText),
          response.status
        );
      }
      const renegotiateData = await response.json();
      return createJsonResponse(createSuccessResponse(renegotiateData));
    } catch (error) {
      console.error("Renegotiate error:", error);
      return createJsonResponse(
        createErrorResponse("Internal server error", error instanceof Error ? error.message : "Unknown error"),
        500
      );
    }
  }
  // 关闭音轨
  async closeTracks(sessionId, tracks, sessionDescription, force) {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/close`;
      const requestBody = {
        tracks
      };
      if (sessionDescription) {
        requestBody.sessionDescription = sessionDescription;
      }
      if (force !== void 0) {
        requestBody.force = force;
      }
      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Authorization": `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Close tracks error:", errorText);
        return createJsonResponse(
          createErrorResponse("Failed to close tracks", errorText),
          response.status
        );
      }
      const closeData = await response.json();
      return createJsonResponse(createSuccessResponse(closeData));
    } catch (error) {
      console.error("Close tracks error:", error);
      return createJsonResponse(
        createErrorResponse("Internal server error", error instanceof Error ? error.message : "Unknown error"),
        500
      );
    }
  }
  // 更新音轨
  async updateTracks(sessionId, tracks) {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/update`;
      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Authorization": `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ tracks })
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Update tracks error:", errorText);
        return createJsonResponse(
          createErrorResponse("Failed to update tracks", errorText),
          response.status
        );
      }
      const updateData = await response.json();
      return createJsonResponse(createSuccessResponse(updateData));
    } catch (error) {
      console.error("Update tracks error:", error);
      return createJsonResponse(
        createErrorResponse("Internal server error", error instanceof Error ? error.message : "Unknown error"),
        500
      );
    }
  }
  // 获取会话状态
  async getSessionState(sessionId) {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}`;
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`
        }
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Get session state error:", errorText);
        return createJsonResponse(
          createErrorResponse("Failed to get session state", errorText),
          response.status
        );
      }
      const sessionData = await response.json();
      return createJsonResponse(createSuccessResponse(sessionData));
    } catch (error) {
      console.error("Get session state error:", error);
      return createJsonResponse(
        createErrorResponse("Internal server error", error instanceof Error ? error.message : "Unknown error"),
        500
      );
    }
  }
  // 处理WebRTC信令
  async handleSignaling(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    const sessionId = url.searchParams.get("sessionId");
    if (!sessionId) {
      return createJsonResponse(
        createErrorResponse("Session ID is required"),
        400
      );
    }
    try {
      const body = await request.json();
      switch (pathname) {
        case "/calls/sessions/new":
          return this.createSession(body.correlationId);
        case "/calls/tracks/add":
          return this.addTracks(sessionId, body.tracks, body.sessionDescription);
        case "/calls/renegotiate":
          return this.renegotiate(sessionId, body.sessionDescription);
        case "/calls/tracks/close":
          return this.closeTracks(sessionId, body.tracks, body.sessionDescription, body.force);
        case "/calls/tracks/update":
          return this.updateTracks(sessionId, body.tracks);
        case "/calls/session/state":
          return this.getSessionState(sessionId);
        default:
          return createJsonResponse(
            createErrorResponse("Unknown endpoint"),
            404
          );
      }
    } catch (error) {
      console.error("Signaling handler error:", error);
      return createJsonResponse(
        createErrorResponse("Failed to process signaling request", error instanceof Error ? error.message : "Unknown error"),
        500
      );
    }
  }
};

// src/index.ts
var rateLimiter = new RateLimiter(100, 6e4);
var src_default = {
  async fetch(request, env, ctx) {
    if (request.method === "OPTIONS") {
      return handleCorsOptions();
    }
    const clientIP = request.headers.get("CF-Connecting-IP") || "unknown";
    if (!rateLimiter.isAllowed(clientIP)) {
      return createJsonResponse(
        createErrorResponse("Rate limit exceeded"),
        429
      );
    }
    const url = new URL(request.url);
    const pathname = url.pathname;
    try {
      if (pathname.startsWith("/api/room") || pathname.startsWith("/ws")) {
        return await handleRoomRequests(request, env);
      } else if (pathname.startsWith("/api/calls")) {
        return await handleCallsRequests(request, env);
      } else if (pathname === "/api/health") {
        return createJsonResponse({
          success: true,
          data: {
            status: "healthy",
            timestamp: (/* @__PURE__ */ new Date()).toISOString(),
            version: "1.0.0"
          }
        });
      } else {
        return createJsonResponse(
          createErrorResponse("Not Found", "The requested endpoint was not found"),
          404
        );
      }
    } catch (error) {
      console.error("Request handling error:", error);
      return createJsonResponse(
        createErrorResponse("Internal Server Error", error instanceof Error ? error.message : "Unknown error"),
        500
      );
    }
  }
};
async function handleRoomRequests(request, env) {
  const roomId = "default-room";
  const durableObjectId = env.ROOM_STATE.idFromName(roomId);
  const roomState = env.ROOM_STATE.get(durableObjectId);
  const url = new URL(request.url);
  const newPathname = url.pathname.replace("/api/room", "");
  url.pathname = newPathname || "/room";
  if (url.pathname === "/ws" || request.headers.get("Upgrade") === "websocket") {
    url.pathname = "/ws";
    const newRequest2 = new Request(url.toString(), request);
    return await roomState.fetch(newRequest2);
  }
  const newRequest = new Request(url.toString(), {
    method: request.method,
    headers: request.headers,
    body: request.body
  });
  return await roomState.fetch(newRequest);
}
__name(handleRoomRequests, "handleRoomRequests");
async function handleCallsRequests(request, env) {
  const callsHandler = new CallsHandler(env);
  const url = new URL(request.url);
  const pathname = url.pathname.replace("/api/calls", "");
  const newUrl = new URL(request.url);
  newUrl.pathname = pathname;
  const newRequest = new Request(newUrl.toString(), {
    method: request.method,
    headers: request.headers,
    body: request.body
  });
  return await callsHandler.handleSignaling(newRequest);
}
__name(handleCallsRequests, "handleCallsRequests");
async function scheduled(event, env, ctx) {
  rateLimiter.cleanup();
  console.log("Scheduled cleanup completed");
}
__name(scheduled, "scheduled");

// ../../../AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// ../../../AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-uSIFVA/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// ../../../AppData/Roaming/npm/node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-uSIFVA/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class ___Facade_ScheduledController__ {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  static {
    __name(this, "__Facade_ScheduledController__");
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof ___Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = /* @__PURE__ */ __name((request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    }, "#fetchDispatcher");
    #dispatcher = /* @__PURE__ */ __name((type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    }, "#dispatcher");
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  RoomState,
  UserState,
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default,
  scheduled
};
//# sourceMappingURL=index.js.map
