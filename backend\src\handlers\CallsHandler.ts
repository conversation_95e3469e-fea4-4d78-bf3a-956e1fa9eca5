import { Env, CallsSession, CallsTrack, SessionDescription } from '../types';
import { createSuccessResponse, createErrorResponse, createJsonResponse } from '../utils';

export class CallsHandler {
  private baseUrl = 'https://rtc.live.cloudflare.com/v1';

  constructor(private env: Env) {}

  // 创建新的WebRTC会话
  async createSession(correlationId?: string): Promise<Response> {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/new`;
      const params = new URLSearchParams();
      
      if (correlationId) {
        params.append('correlationId', correlationId);
      }

      const response = await fetch(`${url}?${params.toString()}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Calls API error:', errorText);
        return createJsonResponse(
          createErrorResponse('Failed to create session', errorText),
          response.status
        );
      }

      const sessionData = await response.json();
      return createJsonResponse(createSuccessResponse(sessionData));
    } catch (error) {
      console.error('Create session error:', error);
      return createJsonResponse(
        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  }

  // 添加音轨到会话
  async addTracks(sessionId: string, tracks: any[], sessionDescription?: SessionDescription): Promise<Response> {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/new`;
      
      const requestBody: any = {
        tracks
      };

      if (sessionDescription) {
        requestBody.sessionDescription = sessionDescription;
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Add tracks error:', errorText);
        return createJsonResponse(
          createErrorResponse('Failed to add tracks', errorText),
          response.status
        );
      }

      const tracksData = await response.json();
      return createJsonResponse(createSuccessResponse(tracksData));
    } catch (error) {
      console.error('Add tracks error:', error);
      return createJsonResponse(
        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  }

  // 重新协商WebRTC会话
  async renegotiate(sessionId: string, sessionDescription: SessionDescription): Promise<Response> {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/renegotiate`;
      
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionDescription }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Renegotiate error:', errorText);
        return createJsonResponse(
          createErrorResponse('Failed to renegotiate', errorText),
          response.status
        );
      }

      const renegotiateData = await response.json();
      return createJsonResponse(createSuccessResponse(renegotiateData));
    } catch (error) {
      console.error('Renegotiate error:', error);
      return createJsonResponse(
        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  }

  // 关闭音轨
  async closeTracks(sessionId: string, tracks: any[], sessionDescription?: SessionDescription, force?: boolean): Promise<Response> {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/close`;
      
      const requestBody: any = {
        tracks
      };

      if (sessionDescription) {
        requestBody.sessionDescription = sessionDescription;
      }

      if (force !== undefined) {
        requestBody.force = force;
      }

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Close tracks error:', errorText);
        return createJsonResponse(
          createErrorResponse('Failed to close tracks', errorText),
          response.status
        );
      }

      const closeData = await response.json();
      return createJsonResponse(createSuccessResponse(closeData));
    } catch (error) {
      console.error('Close tracks error:', error);
      return createJsonResponse(
        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  }

  // 更新音轨
  async updateTracks(sessionId: string, tracks: any[]): Promise<Response> {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/update`;
      
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tracks }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Update tracks error:', errorText);
        return createJsonResponse(
          createErrorResponse('Failed to update tracks', errorText),
          response.status
        );
      }

      const updateData = await response.json();
      return createJsonResponse(createSuccessResponse(updateData));
    } catch (error) {
      console.error('Update tracks error:', error);
      return createJsonResponse(
        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  }

  // 获取会话状态
  async getSessionState(sessionId: string): Promise<Response> {
    try {
      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Get session state error:', errorText);
        return createJsonResponse(
          createErrorResponse('Failed to get session state', errorText),
          response.status
        );
      }

      const sessionData = await response.json();
      return createJsonResponse(createSuccessResponse(sessionData));
    } catch (error) {
      console.error('Get session state error:', error);
      return createJsonResponse(
        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  }

  // 处理WebRTC信令
  async handleSignaling(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const pathname = url.pathname;
    const sessionId = url.searchParams.get('sessionId');

    if (!sessionId) {
      return createJsonResponse(
        createErrorResponse('Session ID is required'),
        400
      );
    }

    try {
      const body = await request.json();

      switch (pathname) {
        case '/calls/sessions/new':
          return this.createSession(body.correlationId);
        
        case '/calls/tracks/add':
          return this.addTracks(sessionId, body.tracks, body.sessionDescription);
        
        case '/calls/renegotiate':
          return this.renegotiate(sessionId, body.sessionDescription);
        
        case '/calls/tracks/close':
          return this.closeTracks(sessionId, body.tracks, body.sessionDescription, body.force);
        
        case '/calls/tracks/update':
          return this.updateTracks(sessionId, body.tracks);
        
        case '/calls/session/state':
          return this.getSessionState(sessionId);
        
        default:
          return createJsonResponse(
            createErrorResponse('Unknown endpoint'),
            404
          );
      }
    } catch (error) {
      console.error('Signaling handler error:', error);
      return createJsonResponse(
        createErrorResponse('Failed to process signaling request', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  }
}
