import React from 'react';
import { User, Channel } from '../types';
import UserAvatar from './UserAvatar';

interface MainContentProps {
  currentChannel: Channel | null;
  user: User;
}

const MainContent: React.FC<MainContentProps> = ({ currentChannel, user }) => {
  if (!currentChannel) {
    return (
      <div className="flex-1 bg-discord-gray-700 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-discord-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.594-.471-3.078-1.343-4.243a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">选择一个语音频道</h2>
          <p className="text-discord-gray-400">
            从左侧选择一个语音频道开始聊天
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-discord-gray-700 flex flex-col">
      {/* 频道标题栏 */}
      <div className="h-12 px-4 flex items-center border-b border-discord-gray-600 bg-discord-gray-800">
        <div className="flex items-center">
          <svg className="w-5 h-5 mr-2 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.594-.471-3.078-1.343-4.243a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
          <h2 className="text-lg font-semibold text-white">{currentChannel.name}</h2>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 语音频道用户列表 */}
        <div className="flex-1 p-4">
          <div className="bg-discord-gray-800 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-discord-gray-400 uppercase tracking-wide mb-3">
              频道成员 ({currentChannel.users.length + 1})
            </h3>
            
            {/* 当前用户 */}
            <div className="user-item mb-2">
              <UserAvatar
                user={user}
                size="medium"
                showStatus={true}
                showVoiceActivity={true}
                className="mr-3"
              />
              <div className="flex-1">
                <div className="text-sm font-medium text-white">
                  {user.nickname} (你)
                </div>
                <div className="text-xs text-discord-gray-400">
                  {user.isMuted ? '已静音' : user.isDeafened ? '已关闭声音' : '正常'}
                </div>
              </div>
            </div>

            {/* 其他用户 */}
            {currentChannel.users.map((channelUser) => (
              <div key={channelUser.id} className="user-item mb-2">
                <UserAvatar
                  user={channelUser}
                  size="medium"
                  showStatus={true}
                  showVoiceActivity={true}
                  className="mr-3"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-white">
                    {channelUser.nickname}
                  </div>
                  <div className="text-xs text-discord-gray-400">
                    {channelUser.isMuted ? '已静音' : channelUser.isDeafened ? '已关闭声音' : '正常'}
                  </div>
                </div>
              </div>
            ))}

            {currentChannel.users.length === 0 && (
              <div className="text-center py-8">
                <div className="text-discord-gray-400 text-sm">
                  频道中暂无其他用户
                </div>
                <div className="text-discord-gray-500 text-xs mt-1">
                  邀请朋友加入聊天吧！
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 侧边栏（可选的聊天或设置） */}
        <div className="w-64 bg-discord-gray-800 border-l border-discord-gray-600 p-4">
          <div className="text-center">
            <div className="w-12 h-12 bg-discord-gray-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
            </div>
            <h4 className="text-sm font-medium text-white mb-2">语音设置</h4>
            <p className="text-xs text-discord-gray-400 mb-4">
              调整你的语音和音频设置
            </p>
            
            <div className="space-y-3">
              <div className="text-left">
                <label className="block text-xs font-medium text-discord-gray-400 mb-1">
                  输入音量
                </label>
                <div className="w-full bg-discord-gray-700 rounded-full h-2">
                  <div 
                    className="bg-discord-green h-2 rounded-full transition-all duration-200"
                    style={{ width: `${user.volume}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="text-left">
                <label className="block text-xs font-medium text-discord-gray-400 mb-1">
                  输出音量
                </label>
                <div className="w-full bg-discord-gray-700 rounded-full h-2">
                  <div 
                    className="bg-discord-blurple h-2 rounded-full transition-all duration-200"
                    style={{ width: '80%' }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainContent;
