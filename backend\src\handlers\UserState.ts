
import { User } from '../types';
import { formatTimestamp } from '../utils';

export class UserState implements DurableObject {
  private user: User | null = null;
  private webSocket: WebSocket | null = null;

  constructor(private state: DurableObjectState, private env: any) {}

  async fetch(request: Request): Promise<Response> {
    if (request.headers.get("Upgrade") !== "websocket") {
      return new Response("Expected a WebSocket connection", { status: 400 });
    }

    const pair = new WebSocketPair();
    const [client, server] = Object.values(pair);

    server.accept();
    this.webSocket = server;

    server.addEventListener("message", (event: any) => {
      console.log("UserState received message:", event.data);
    });

    server.addEventListener("close", () => {
      this.webSocket = null;
    });

    return new Response(null, { status: 101, webSocket: client });
  }

  async setUser(user: User) {
    this.user = user;
    await this.state.storage.put("user", this.user);
  }
} 