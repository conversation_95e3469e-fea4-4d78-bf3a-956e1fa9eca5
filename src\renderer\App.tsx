import React, { useState, useEffect, useCallback } from 'react';
import { User, Channel, VoiceState, AppEvent } from './types';
import { generateRandomNickname, generateId, userStorage } from './utils';
import { useWebSocket } from './hooks/useWebSocket';
import { useWebRTC } from './hooks/useWebRTC';
import TitleBar from './components/TitleBar';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import VoiceControls from './components/VoiceControls';
import ConnectionStatus from './components/ConnectionStatus';

const App: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [channels, setChannels] = useState<Channel[]>([]);
  const [currentChannel, setCurrentChannel] = useState<Channel | null>(null);
  const [voiceState, setVoiceState] = useState<VoiceState>({
    isConnected: false,
    isMuted: false,
    isDeafened: false,
    volume: 100,
  });

  // 处理应用事件
  const handleAppEvent = useCallback((event: AppEvent) => {
    switch (event.type) {
      case 'USER_JOIN':
        // 处理用户加入
        break;
      case 'USER_LEAVE':
        // 处理用户离开
        break;
      case 'CHANNEL_CREATE':
        setChannels(prev => [...prev, event.payload]);
        break;
      case 'CHANNEL_DELETE':
        setChannels(prev => prev.filter(c => c.id !== event.payload.channelId));
        if (currentChannel?.id === event.payload.channelId) {
          setCurrentChannel(null);
        }
        break;
      case 'CHANNEL_UPDATE':
        setChannels(prev => prev.map(c =>
          c.id === event.payload.id ? event.payload : c
        ));
        break;
      default:
        console.log('Unhandled app event:', event);
    }
  }, [currentChannel]);

  // 初始化用户
  useEffect(() => {
    const initUser = () => {
      // 尝试从本地存储获取上次的昵称
      const lastNickname = userStorage.getLastNickname();
      const userPreferences = userStorage.getUserPreferences();

      const newUser: User = {
        id: generateId(),
        nickname: lastNickname || generateRandomNickname(),
        isConnected: false,
        isMuted: false,
        isDeafened: false,
        isSpeaking: false,
        volume: userPreferences?.volume || 100,
        joinedAt: new Date(),
      };

      setUser(newUser);

      // 如果没有保存的昵称，保存当前生成的昵称
      if (!lastNickname) {
        userStorage.saveLastNickname(newUser.nickname);
      }

      // 增加会话计数
      userStorage.incrementSessionCount();
    };

    initUser();
  }, []);

  // 初始化默认频道
  useEffect(() => {
    const defaultChannels: Channel[] = [
      {
        id: generateId(),
        name: '大厅',
        type: 'voice',
        userCount: 0,
        users: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: generateId(),
        name: '游戏频道',
        type: 'voice',
        userCount: 0,
        users: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    setChannels(defaultChannels);
  }, []);

  // 初始化WebSocket和WebRTC hooks（只有在用户初始化后才创建）
  const webSocket = user ? useWebSocket({
    user,
    onEvent: handleAppEvent,
  }) : null;

  const webRTC = user ? useWebRTC({
    user,
    voiceState,
    onVoiceStateChange: (newState) => setVoiceState(prev => ({ ...prev, ...newState })),
  }) : null;

  const handleChannelSelect = async (channel: Channel) => {
    // 如果已经在语音频道中，先离开
    if (voiceState.isConnected && voiceState.currentChannelId && webRTC && webSocket) {
      webRTC.disconnectFromVoiceChannel();
      webSocket.leaveChannel(voiceState.currentChannelId);
    }

    setCurrentChannel(channel);

    // 如果是语音频道，尝试连接
    if (channel.type === 'voice' && webRTC && webSocket) {
      try {
        await webRTC.connectToVoiceChannel(channel.id);
        webSocket.joinChannel(channel.id);
      } catch (error) {
        console.error('Failed to connect to voice channel:', error);
      }
    }
  };

  const handleChannelCreate = (name: string, type: 'voice' | 'text' = 'voice') => {
    if (webSocket) {
      webSocket.createChannel(name, type);
    }
  };

  const handleChannelDelete = (channelId: string) => {
    if (webSocket) {
      webSocket.deleteChannel(channelId);
    }
  };

  const handleChannelRename = (channelId: string, newName: string) => {
    if (webSocket) {
      webSocket.renameChannel(channelId, newName);
    }
  };

  const handleUserUpdate = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);

      // 保存用户偏好设置
      if (updates.nickname) {
        userStorage.saveLastNickname(updates.nickname);
      }

      if (updates.volume !== undefined) {
        const preferences = userStorage.getUserPreferences();
        userStorage.saveUserPreferences({
          ...preferences,
          volume: updates.volume,
        });
      }

      // 同步到WebSocket
      if (webSocket) {
        webSocket.updateUserState(updates);
      }
    }
  };

  const handleVoiceStateChange = (newState: Partial<VoiceState>) => {
    setVoiceState(prev => ({ ...prev, ...newState }));

    // 更新WebSocket中的用户状态
    if (user && webRTC && webSocket) {
      const updatedUser = {
        ...user,
        isMuted: newState.isMuted ?? voiceState.isMuted,
        isDeafened: newState.isDeafened ?? voiceState.isDeafened,
        volume: newState.volume ?? voiceState.volume,
        isSpeaking: webRTC.isSpeaking,
      };

      setUser(updatedUser);

      webSocket.updateUserState({
        isMuted: updatedUser.isMuted,
        isDeafened: updatedUser.isDeafened,
        volume: updatedUser.volume,
        isSpeaking: updatedUser.isSpeaking,
      });
    }
  };

  if (!user) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-discord-gray-900">
        <div className="text-white text-xl">正在初始化...</div>
      </div>
    );
  }

  // 显示连接状态
  const connectionStatus = webSocket?.isConnected ? '已连接' : '连接中...';
  const hasWebRTCError = webRTC?.error || webSocket?.error;

  return (
    <div className="h-screen w-screen flex flex-col bg-discord-gray-900 text-gray-100">
      <TitleBar />

      {/* 连接状态 */}
      {webSocket && (
        <ConnectionStatus
          isConnected={webSocket.isConnected}
          isConnecting={!webSocket.isConnected && webSocket.reconnectAttempts === 0}
          error={webSocket.error || webRTC?.error}
          reconnectAttempts={webSocket.reconnectAttempts}
          onReconnect={webSocket.reconnect}
        />
      )}

      <div className="flex flex-1 overflow-hidden">
        <Sidebar
          user={user}
          channels={channels}
          currentChannel={currentChannel}
          onChannelSelect={handleChannelSelect}
          onChannelCreate={handleChannelCreate}
          onChannelDelete={handleChannelDelete}
          onChannelRename={handleChannelRename}
          onUserUpdate={handleUserUpdate}
        />
        <div className="flex-1 flex flex-col">
          <MainContent
            currentChannel={currentChannel}
            user={user}
          />
          <VoiceControls
            user={user}
            voiceState={voiceState}
            onVoiceStateChange={handleVoiceStateChange}
            webRTC={webRTC ? {
              toggleMute: webRTC.toggleMute,
              toggleDeafen: webRTC.toggleDeafen,
              setVolume: webRTC.setVolume,
              isSpeaking: webRTC.isSpeaking,
              currentVolume: webRTC.currentVolume,
              deviceManager: webRTC.deviceManager,
            } : undefined}
          />
        </div>
      </div>
    </div>
  );
};

export default App;
