import React, { useState, useEffect, useCallback } from 'react';
import { User, Channel, VoiceState, AppEvent } from './types';
import { generateRandomNickname, generateId, userStorage } from './utils';
import { useWebSocket } from './hooks/useWebSocket';
import { useWebRTC } from './hooks/useWebRTC';
import TitleBar from './components/TitleBar';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import VoiceControls from './components/VoiceControls';
import ConnectionStatus from './components/ConnectionStatus';

const App: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [channels, setChannels] = useState<Channel[]>([]);
  const [currentChannel, setCurrentChannel] = useState<Channel | null>(null);
  const [voiceState, setVoiceState] = useState<VoiceState>({
    isConnected: false,
    isMuted: false,
    isDeafened: false,
    volume: 100,
  });

  // 处理应用事件
  const handleAppEvent = useCallback((event: AppEvent) => {
    console.log('App event received:', event);
    switch (event.type) {
      case 'ROOM_STATE_UPDATE':
        // 更新房间状态
        const roomState = event.payload;
        if (roomState.users) {
          setUsers(roomState.users);
        }
        if (roomState.channels) {
          setChannels(roomState.channels);
        }
        break;
      case 'USER_JOIN':
        console.log('处理USER_JOIN事件:', event.payload);
        setUsers(prev => {
          const existingIndex = prev.findIndex(u => u.id === event.payload.id);
          if (existingIndex >= 0) {
            // 更新现有用户
            const updated = [...prev];
            updated[existingIndex] = { ...event.payload, isConnected: true };
            return updated;
          } else {
            // 添加新用户
            return [...prev, { ...event.payload, isConnected: true }];
          }
        });
        break;
      case 'USER_LEAVE':
        console.log('处理USER_LEAVE事件:', event.payload);
        setUsers(prev => prev.filter(u => u.id !== event.payload.userId));
        break;
      case 'USER_UPDATE':
        setUsers(prev => prev.map(u => 
          u.id === event.payload.id ? { ...u, ...event.payload } : u
        ));
        break;
      case 'USER_LIST_UPDATE':
        setUsers(event.payload);
        break;
      case 'CHANNEL_CREATE':
        setChannels(prev => [...prev, event.payload]);
        break;
      case 'CHANNEL_DELETE':
        setChannels(prev => prev.filter(c => c.id !== event.payload.channelId));
        if (currentChannel?.id === event.payload.channelId) {
          setCurrentChannel(null);
        }
        break;
      case 'CHANNEL_UPDATE':
        setChannels(prev => prev.map(c =>
          c.id === event.payload.id ? event.payload : c
        ));
        break;
      case 'USER_JOIN_CHANNEL':
        console.log('处理USER_JOIN_CHANNEL事件:', event.payload);
        setChannels(prev => {
          return prev.map(c => {
            if (c.id === event.payload.channelId) {
              // 确保不重复添加用户
              if (event.payload.user && !c.users.some(u => u.id === event.payload.user.id)) {
                console.log(`添加用户 ${event.payload.user.nickname} 到频道 ${c.name}`);
                return {
                  ...c,
                  users: [...c.users, event.payload.user],
                  userCount: c.userCount + 1,
                };
              }
            }
            return c;
          });
        });
        break;
      case 'USER_LEAVE_CHANNEL':
        console.log('处理USER_LEAVE_CHANNEL事件:', event.payload);
        setChannels(prev => prev.map(c => {
          if (c.id === event.payload.channelId) {
            const newUsers = c.users.filter(u => u.id !== event.payload.userId);
            console.log(`从频道 ${c.name} 移除用户，剩余用户数: ${newUsers.length}`);
            return {
              ...c,
              users: newUsers,
              userCount: newUsers.length,
            };
          }
          return c;
        }));
        break;
      default:
        console.log('Unhandled app event:', event);
    }
  }, [currentChannel]);

  // 初始化用户
  useEffect(() => {
    const initUser = () => {
      let existingUser = userStorage.getUser();

      if (existingUser) {
        // 如果找到了旧用户，更新其在线状态并使用
        existingUser.isConnected = false;
        setUser(existingUser);
      } else {
        // 如果是新用户，则创建一个
        const newUser: User = {
          id: generateId(),
          nickname: generateRandomNickname(),
          isConnected: false,
          isMuted: false,
          isDeafened: false,
          isSpeaking: false,
          volume: 100,
          joinedAt: new Date(),
        };
        userStorage.saveUser(newUser);
        setUser(newUser);
      }
      
      // 增加会话计数
      userStorage.incrementSessionCount();
    };

    initUser();
  }, []);

  // 初始化默认频道
  useEffect(() => {
    const defaultChannels: Channel[] = [
      {
        id: generateId(),
        name: '大厅',
        type: 'voice',
        userCount: 0,
        users: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: generateId(),
        name: '游戏频道',
        type: 'voice',
        userCount: 0,
        users: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    setChannels(defaultChannels);
  }, []);

  // 初始化WebSocket和WebRTC hooks（始终调用，但在内部处理null用户）
  const webSocket = useWebSocket({
    user,
    onEvent: handleAppEvent,
  });

  const webRTC = useWebRTC({
    user,
    voiceState,
    onVoiceStateChange: (newState) => setVoiceState(prev => ({ ...prev, ...newState })),
  });

  const handleChannelSelect = async (channel: Channel) => {
    if (!webSocket.isConnected) {
      console.warn('WebSocket未连接，正在尝试连接...');
      
      // 显示正在连接的消息，可以通过状态或Toast实现
      // ...
      
      // 延迟执行，给WebSocket一些连接时间
      setTimeout(() => {
        if (webSocket.isConnected) {
          handleChannelSelect(channel); // 递归调用，尝试再次选择频道
        } else {
          console.error('WebSocket连接失败，无法加入频道');
          // 显示连接失败的消息，可以通过状态或Toast实现
          // ...
        }
      }, 2000);
      return;
    }

    // 如果已经在语音频道中，先离开
    if (voiceState.isConnected && voiceState.currentChannelId) {
      console.log(`离开当前频道: ${voiceState.currentChannelId}`);
      webRTC.disconnectFromVoiceChannel();
      webSocket.leaveChannel(voiceState.currentChannelId);
    }

    console.log(`选择频道: ${channel.name} (${channel.id})`);
    setCurrentChannel(channel);

    // 如果是语音频道，尝试连接
    if (channel.type === 'voice') {
      try {
        console.log(`尝试连接语音频道: ${channel.name}`);
        await webRTC.connectToVoiceChannel(channel.id);
        webSocket.joinChannel(channel.id);
        console.log(`成功加入语音频道: ${channel.name}`);
      } catch (error) {
        console.error('连接语音频道失败:', error);
        // 显示错误消息，可以通过状态或Toast实现
        // ...
      }
    } else {
      // 对于文本频道，只需加入不需要WebRTC
      webSocket.joinChannel(channel.id);
      console.log(`成功加入文本频道: ${channel.name}`);
    }
  };

  const handleChannelCreate = (name: string, type: 'voice' | 'text' = 'voice') => {
    webSocket.createChannel(name, type);
  };

  const handleChannelDelete = (channelId: string) => {
    webSocket.deleteChannel(channelId);
  };

  const handleChannelRename = (channelId: string, newName: string) => {
    webSocket.renameChannel(channelId, newName);
  };

  const handleUserUpdate = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      userStorage.saveUser(updatedUser); // 保存整个用户对象

      // 同步到WebSocket
      webSocket.updateUserState(updates);
    }
  };

  const handleVoiceStateChange = (newState: Partial<VoiceState>) => {
    setVoiceState(prev => ({ ...prev, ...newState }));

    // 更新WebSocket中的用户状态
    if (user) {
      const updatedUser = {
        ...user,
        isMuted: newState.isMuted ?? voiceState.isMuted,
        isDeafened: newState.isDeafened ?? voiceState.isDeafened,
        volume: newState.volume ?? voiceState.volume,
        isSpeaking: webRTC.isSpeaking,
      };

      setUser(updatedUser);

      webSocket.updateUserState({
        isMuted: updatedUser.isMuted,
        isDeafened: updatedUser.isDeafened,
        volume: updatedUser.volume,
        isSpeaking: updatedUser.isSpeaking,
      });
    }
  };

  if (!user) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-discord-gray-900">
        <div className="text-white text-xl">正在初始化...</div>
      </div>
    );
  }

  // 显示连接状态
  const connectionStatus = webSocket.isConnected ? '已连接' : '连接中...';
  const hasWebRTCError = webRTC.error || webSocket.error;

  return (
    <div className="h-screen w-screen flex flex-col bg-discord-gray-900 text-gray-100">
      <TitleBar />

      {/* 连接状态 */}
      <ConnectionStatus
        isConnected={webSocket.isConnected}
        isConnecting={!webSocket.isConnected && webSocket.reconnectAttempts === 0}
        error={webSocket.error || webRTC.error}
        reconnectAttempts={webSocket.reconnectAttempts}
        onReconnect={webSocket.reconnect}
      />

      <div className="flex flex-1 overflow-hidden">
        <Sidebar
          user={user}
          users={users}
          channels={channels}
          currentChannel={currentChannel}
          onChannelSelect={handleChannelSelect}
          onChannelCreate={handleChannelCreate}
          onChannelDelete={handleChannelDelete}
          onChannelRename={handleChannelRename}
          onUserUpdate={handleUserUpdate}
        />
        <div className="flex-1 flex flex-col">
          <MainContent
            currentChannel={currentChannel}
            user={user}
          />
          <VoiceControls
            user={user}
            voiceState={voiceState}
            onVoiceStateChange={handleVoiceStateChange}
            webRTC={{
              toggleMute: webRTC.toggleMute,
              toggleDeafen: webRTC.toggleDeafen,
              setVolume: webRTC.setVolume,
              isSpeaking: webRTC.isSpeaking,
              currentVolume: webRTC.currentVolume,
              deviceManager: webRTC.deviceManager,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default App;
