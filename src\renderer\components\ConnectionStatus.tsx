import React from 'react';

interface ConnectionStatusProps {
  isConnected: boolean;
  isConnecting?: boolean;
  error?: string | null;
  reconnectAttempts?: number;
  onReconnect?: () => void;
  className?: string;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  isConnecting = false,
  error,
  reconnectAttempts = 0,
  onReconnect,
  className = '',
}) => {
  if (isConnected) {
    return null; // 连接正常时不显示
  }

  const getStatusColor = () => {
    if (error) return 'bg-red-600';
    if (isConnecting || reconnectAttempts > 0) return 'bg-yellow-600';
    return 'bg-gray-600';
  };

  const getStatusText = () => {
    if (error) return '连接失败';
    if (reconnectAttempts > 0) return `重连中... (${reconnectAttempts}/5)`;
    if (isConnecting) return '连接中...';
    return '未连接';
  };

  const getStatusIcon = () => {
    if (error) {
      return (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      );
    }

    if (isConnecting || reconnectAttempts > 0) {
      return (
        <svg className="w-4 h-4 animate-spin" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
        </svg>
      );
    }

    return (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 6.707 6.293a1 1 0 00-1.414 1.414L8.586 11l-3.293 3.293a1 1 0 001.414 1.414L10 12.414l3.293 3.293a1 1 0 001.414-1.414L11.414 11l3.293-3.293z" clipRule="evenodd" />
      </svg>
    );
  };

  return (
    <div className={`${getStatusColor()} text-white px-4 py-2 text-sm flex items-center justify-center space-x-2 ${className}`}>
      {getStatusIcon()}
      <span>{getStatusText()}</span>
      
      {error && onReconnect && (
        <button 
          onClick={onReconnect}
          className="ml-2 px-2 py-1 bg-white bg-opacity-20 rounded text-xs hover:bg-opacity-30 transition-colors duration-200"
        >
          重新连接
        </button>
      )}
    </div>
  );
};

export default ConnectionStatus;
