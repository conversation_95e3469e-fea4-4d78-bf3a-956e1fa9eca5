const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动 OPZ Voice Chat 开发环境...');

// 检查必要的文件
const requiredFiles = [
  'src/main/main.ts',
  'src/renderer/index.tsx',
  'webpack.config.js'
];

console.log('📋 检查必要文件...');
for (const file of requiredFiles) {
  if (!fs.existsSync(file)) {
    console.error(`❌ 缺少必要文件: ${file}`);
    process.exit(1);
  }
}
console.log('✅ 所有必要文件都存在');

// 清理旧的构建文件
console.log('🧹 清理旧的构建文件...');
if (fs.existsSync('dist')) {
  fs.rmSync('dist', { recursive: true, force: true });
}

// 启动Webpack开发服务器
console.log('🔨 启动Webpack构建...');
const webpack = spawn('npm', ['run', 'dev'], {
  stdio: 'pipe',
  shell: true
});

webpack.stdout.on('data', (data) => {
  console.log(`[Webpack] ${data}`);
});

webpack.stderr.on('data', (data) => {
  console.error(`[Webpack Error] ${data}`);
});

// 等待构建完成后启动Electron
let electronStarted = false;
const checkAndStartElectron = () => {
  if (!electronStarted && fs.existsSync('dist/index.html') && fs.existsSync('dist/main.js')) {
    electronStarted = true;
    console.log('🖥️  启动Electron应用...');
    
    const electron = spawn('npm', ['start'], {
      stdio: 'inherit',
      shell: true
    });
    
    electron.on('close', (code) => {
      console.log(`\n🛑 Electron应用已关闭 (退出码: ${code})`);
      webpack.kill();
      process.exit(code);
    });
  }
};

// 监听文件变化
const checkInterval = setInterval(() => {
  checkAndStartElectron();
}, 1000);

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭开发环境...');
  clearInterval(checkInterval);
  webpack.kill();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭开发环境...');
  clearInterval(checkInterval);
  webpack.kill();
  process.exit(0);
});

console.log('⏳ 等待构建完成...');
console.log('💡 提示: 使用 Ctrl+C 停止开发环境');
