# Discord风格的语音聊天应用

# OPZ Voice Chat

一个基于Electron的Discord风格语音聊天应用，使用Cloudflare Workers和Cloudflare Calls API实现实时通信功能。

## ✨ 特性

- 🎤 **实时语音通信** - 基于WebRTC和Cloudflare Calls API
- 🏠 **频道管理** - 创建、删除、重命名语音频道
- 👤 **用户系统** - 随机昵称生成，即用即走
- 🔊 **音频控制** - 静音、关闭声音、音量调节
- 📊 **语音活动检测** - 实时显示说话状态
- 🎨 **Discord风格UI** - 熟悉的界面设计
- 💾 **本地存储** - 保存用户偏好设置
- 🖥️ **Windows专用** - 针对Windows平台优化

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn
- Windows 10/11

### 安装依赖

```bash
npm install
```

### 开发环境

```bash
# 启动开发环境
npm run electron:dev

# 或使用自定义脚本
npm run dev:script
```

### 构建应用

```bash
# 开发构建
npm run build:dev

# 生产构建
npm run build

# 打包为可执行文件
npm run dist:win

# 使用自定义构建脚本
npm run build:script
```

## 🏗️ 项目结构

```
opz/
├── src/
│   ├── main/                 # Electron主进程
│   │   ├── main.ts          # 主进程入口
│   │   └── preload.ts       # 预加载脚本
│   └── renderer/            # 渲染进程
│       ├── components/      # React组件
│       ├── hooks/          # 自定义Hooks
│       ├── types/          # TypeScript类型
│       ├── utils/          # 工具函数
│       ├── styles/         # 样式文件
│       ├── App.tsx         # 主应用组件
│       └── index.tsx       # 渲染进程入口
├── backend/                 # Cloudflare Workers后端
│   ├── src/
│   │   ├── handlers/       # API处理器
│   │   ├── types/          # 类型定义
│   │   ├── utils/          # 工具函数
│   │   └── index.ts        # Worker入口
│   └── wrangler.toml       # Cloudflare配置
├── scripts/                # 构建脚本
├── assets/                 # 资源文件
└── dist/                   # 构建输出
```

## 🔧 技术栈

### 前端
- **Electron** - 桌面应用框架
- **React 19** - UI框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Webpack** - 模块打包

### 后端
- **Cloudflare Workers** - 无服务器后端
- **Cloudflare Calls API** - WebRTC通信
- **Durable Objects** - 状态管理

### 音频处理
- **Web Audio API** - 音频处理
- **WebRTC** - 实时通信
- **MediaDevices API** - 设备访问

## 📱 功能说明

### 语音通信
- 基于Cloudflare Calls API的高质量语音通信
- 自动回声消除和噪音抑制
- 实时语音活动检测
- 音量控制和静音功能

### 频道管理
- 创建和删除语音频道
- 频道重命名
- 用户列表显示
- 频道切换

### 用户体验
- 随机昵称生成
- 用户偏好设置保存
- Discord风格的界面设计
- 实时连接状态显示

## 🔐 隐私和安全

- 无需注册，即用即走
- 本地存储用户偏好
- 端到端加密通信（通过Cloudflare）
- 不收集个人信息

## 📦 打包和分发

应用支持多种打包格式：

- **NSIS安装包** - 标准Windows安装程序
- **便携版** - 免安装可执行文件
- **开发版** - 未打包的开发构建

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

ISC License

## 🙏 致谢

- [Electron](https://electronjs.org/) - 跨平台桌面应用框架
- [Cloudflare](https://cloudflare.com/) - 边缘计算和WebRTC服务
- [React](https://reactjs.org/) - 用户界面库
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架

## 特性

- 仅支持Windows平台
- 无需注册，即用即走
- 随机生成用户昵称
- 支持创建、删除、重命名频道
- 实时语音通话
- 语音活动指示器
- 静音/解除静音控制
- 音量调节

## 技术栈

### 前端
- Electron
- React
- TypeScript
- Tailwind CSS
- Simple-Peer (WebRTC)

### 后端
- Cloudflare Workers
- Cloudflare Durable Objects (状态存储)
- Cloudflare Calls API (语音通信)

