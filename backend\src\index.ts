import { Env } from './types';
import { RoomState } from './handlers/RoomState';
import { CallsHandler } from './handlers/CallsHandler';
import { 
  handleCorsOptions, 
  createJsonResponse, 
  createErrorResponse, 
  withErrorHandling,
  RateLimiter 
} from './utils';

// 导出Durable Object类
export { RoomState };

// 全局限流器
const rateLimiter = new RateLimiter(100, 60000); // 每分钟100个请求

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    // 处理CORS预检请求
    if (request.method === 'OPTIONS') {
      return handleCorsOptions();
    }

    // 获取客户端IP进行限流
    const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
    if (!rateLimiter.isAllowed(clientIP)) {
      return createJsonResponse(
        createErrorResponse('Rate limit exceeded'),
        429
      );
    }

    const url = new URL(request.url);
    const pathname = url.pathname;

    try {
      // 路由处理
      if (pathname.startsWith('/api/room') || pathname.startsWith('/ws')) {
        return await handleRoomRequests(request, env);
      } else if (pathname.startsWith('/api/calls')) {
        return await handleCallsRequests(request, env);
      } else if (pathname === '/api/health') {
        return createJsonResponse({
          success: true,
          data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0'
          }
        });
      } else {
        return createJsonResponse(
          createErrorResponse('Not Found', 'The requested endpoint was not found'),
          404
        );
      }
    } catch (error) {
      console.error('Request handling error:', error);
      return createJsonResponse(
        createErrorResponse('Internal Server Error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  },
};

// 处理房间相关请求
async function handleRoomRequests(request: Request, env: Env): Promise<Response> {
  // 获取房间状态的Durable Object
  const roomId = 'default-room'; // 目前只有一个房间
  const durableObjectId = env.ROOM_STATE.idFromName(roomId);
  const roomState = env.ROOM_STATE.get(durableObjectId);

  // 转发请求到Durable Object
  const url = new URL(request.url);
  
  // 移除API前缀
  const newPathname = url.pathname.replace('/api/room', '');
  url.pathname = newPathname || '/room';

  // WebSocket升级请求
  if (url.pathname === '/ws' || request.headers.get('Upgrade') === 'websocket') {
    url.pathname = '/ws';
    const newRequest = new Request(url.toString(), request);
    return await roomState.fetch(newRequest);
  }

  // 普通HTTP请求
  const newRequest = new Request(url.toString(), {
    method: request.method,
    headers: request.headers,
    body: request.body,
  });

  return await roomState.fetch(newRequest);
}

// 处理Cloudflare Calls相关请求
async function handleCallsRequests(request: Request, env: Env): Promise<Response> {
  const callsHandler = new CallsHandler(env);
  
  const url = new URL(request.url);
  const pathname = url.pathname.replace('/api/calls', '');
  
  // 创建新的URL用于处理
  const newUrl = new URL(request.url);
  newUrl.pathname = pathname;
  
  const newRequest = new Request(newUrl.toString(), {
    method: request.method,
    headers: request.headers,
    body: request.body,
  });

  return await callsHandler.handleSignaling(newRequest);
}

// 定期清理任务（可选）
export async function scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
  // 清理限流器
  rateLimiter.cleanup();
  
  // 这里可以添加其他定期清理任务
  console.log('Scheduled cleanup completed');
}
