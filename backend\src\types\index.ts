// 环境变量类型
export interface Env {
  ROOM_STATE: DurableObjectNamespace;
  USER_STATE: DurableObjectNamespace;
  ROOM_KV: KVNamespace;
  CLOUDFLARE_CALLS_APP_ID: string;
  CLOUDFLARE_CALLS_SECRET: string;
  CORS_ORIGIN: string;
}

// 用户类型
export interface User {
  id: string;
  nickname: string;
  isConnected: boolean;
  isMuted: boolean;
  isDeafened: boolean;
  isSpeaking: boolean;
  volume: number;
  joinedAt: string;
  sessionId?: string;
}

// 频道类型
export interface Channel {
  id: string;
  name: string;
  type: 'voice' | 'text';
  userCount: number;
  users: User[];
  createdAt: string;
  updatedAt: string;
}

// 房间类型
export interface Room {
  id: string;
  name: string;
  channels: Channel[];
  users: User[];
  createdAt: string;
  updatedAt: string;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Cloudflare Calls相关类型
export interface CallsSession {
  sessionId: string;
  appId: string;
  tracks: CallsTrack[];
}

export interface CallsTrack {
  trackName: string;
  mid: string;
  location: 'local' | 'remote';
  sessionId?: string;
  status: 'active' | 'inactive' | 'waiting';
}

export interface SessionDescription {
  sdp: string;
  type: 'offer' | 'answer';
}

// 请求类型
export interface CreateChannelRequest {
  name: string;
  type?: 'voice' | 'text';
}

export interface UpdateChannelRequest {
  name?: string;
}

export interface JoinChannelRequest {
  userId: string;
  channelId: string;
}

export interface UpdateUserRequest {
  nickname?: string;
  isMuted?: boolean;
  isDeafened?: boolean;
  volume?: number;
}

// WebRTC信令相关类型
export interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'join' | 'leave';
  payload: any;
  from: string;
  to?: string;
  channelId: string;
}

// 事件类型
export type RoomEvent = 
  | { type: 'USER_JOIN'; payload: User }
  | { type: 'USER_LEAVE'; payload: { userId: string } }
  | { type: 'USER_UPDATE'; payload: User }
  | { type: 'CHANNEL_CREATE'; payload: Channel }
  | { type: 'CHANNEL_DELETE'; payload: { channelId: string } }
  | { type: 'CHANNEL_UPDATE'; payload: Channel }
  | { type: 'VOICE_STATE_UPDATE'; payload: { userId: string; state: any } }
  | { type: 'SIGNALING'; payload: SignalingMessage };

// Durable Object状态类型
export interface RoomStateData {
  room: Room;
  connections: Map<string, WebSocket>;
  lastActivity: string;
}

export interface UserStateData {
  user: User;
  connectionId?: string;
  lastSeen: string;
}
