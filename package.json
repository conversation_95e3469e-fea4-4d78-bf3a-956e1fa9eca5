{"name": "opz", "version": "1.0.0", "description": "这是一个基于Electron的Discord风格语音聊天应用，使用Cloudflare Workers和Cloudflare Calls API实现实时通信功能。", "main": "dist/main.js", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "start": "electron .", "dev": "webpack --mode development --watch", "electron:dev": "concurrently \"npm run dev\" \"wait-on dist/index.html && electron .\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "discord", "voice", "chat", "webrtc"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^24.0.7", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "css-loader": "^7.1.2", "electron": "^37.1.0", "html-webpack-plugin": "^5.6.3", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "wait-on": "^8.0.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "simple-peer": "^9.11.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}}