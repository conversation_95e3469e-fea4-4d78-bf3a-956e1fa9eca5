// @ts-ignore entry point will get replaced
import worker from "__ENTRY_POINT__";
import { isRoutingRuleMatch } from "./pages-dev-util";

// @ts-ignore entry point will get replaced
export * from "__ENTRY_POINT__";

// @ts-ignore routes are injected
const routes = __ROUTES__;

export default <ExportedHandler<{ ASSETS: Fetcher }>>{
	fetch(request, env, context) {
		const { pathname } = new URL(request.url);

		for (const exclude of routes.exclude) {
			if (isRoutingRuleMatch(pathname, exclude)) {
				return env.ASSETS.fetch(request);
			}
		}

		for (const include of routes.include) {
			if (isRoutingRuleMatch(pathname, include)) {
				const workerAsHandler = worker as ExportedHandler;
				if (workerAsHandler.fetch === undefined) {
					throw new TypeError("Entry point missing `fetch` handler");
				}
				return workerAsHandler.fetch(request, env, context);
			}
		}

		return env.ASSETS.fetch(request);
	},
};
