{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../ts/build/install.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,IAAI,CAAC;AACpE,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAC5B,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAE,cAAc,EAAY,YAAY,EAAE,MAAM,YAAY,CAAC;AAEpE;;;GAGG;AAEH,MAAM,cAAc,GAAwC;IAC1D,KAAK,EAAE,gBAAgB;IACvB,KAAK,EAAE,eAAe;IACtB,MAAM,EAAE,cAAc;CACvB,CAAC;AAEF,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAClD,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,sCAAsC,CAAC;AACtF,MAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AAEzD,SAAe,OAAO;;QACpB,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,EAAE;YACR,OAAO,CAAC,KAAK,CACX,uHAAuH,CACxH,CAAC;YACF,OAAO,QAAQ,EAAE,CAAC;SACnB;QAED,MAAM,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,CAAC,KAAK,CAAC,oDAAoD,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;YACvF,OAAO,QAAQ,EAAE,CAAC;SACnB;QAED,OAAO,CAAC,GAAG,CACT,8CAA8C,GAAG,CAAC,WAAW,OAAO,OAAO,CAAC,QAAQ,KAAK,CAC1F,CAAC;QACF,MAAM,QAAQ,CAAC,GAAG,OAAO,uBAAuB,OAAO,IAAI,QAAQ,IAAI,GAAG,CAAC,UAAU,OAAO,CAAC,CAAC;QAE9F,IAAI;YACF,OAAO,CAAC,WAAW,CAAC,CAAC;SACtB;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7D,OAAO,QAAQ,EAAE,CAAC;SACnB;QAED,eAAe,EAAE,CAAC;QAClB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;CAAA;AAED,SAAS,iBAAiB,CAAC,OAAiB;IAC1C,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QAChD,MAAM,MAAM,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;QAC3C,IAAI,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;SAC3E;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,QAAQ;IACf,OAAO,CAAC,KAAK,CAAC,sEAAsE,CAAC,CAAC;AACxF,CAAC;AAED,SAAe,QAAQ,CAAC,GAAW;;QACjC,OAAO,IAAI,OAAO,CAAU,OAAO,CAAC,EAAE;YACpC,MAAM,OAAO,GAAG,CAAC,GAAU,EAAE,EAAE;gBAC7B,OAAO,CAAC,KAAK,CAAC,mCAAmC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBACrF,OAAO,CAAC,KAAK,CAAC,CAAC;YACjB,CAAC,CAAC;YAEF,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;gBACzB,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE;oBACxB,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACxC,OAAO;iBACR;gBAED,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE;oBAC5C,OAAO,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,UAAU,SAAS,GAAG,EAAE,CAAC,CAAC;oBAC1D,OAAO,CAAC,KAAK,CAAC,CAAC;oBACf,OAAO;iBACR;gBAED,QAAQ,CAAC,GAAG,EAAE,iBAAiB,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;CAAA;AAED,SAAS,eAAe;IACtB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAClD,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAC5E,CAAC;AAED,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;IACpB,OAAO,CAAC,KAAK,CAAC,2DAA2D,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;IACtF,QAAQ,EAAE,CAAC;AACb,CAAC,CAAC,CAAC"}