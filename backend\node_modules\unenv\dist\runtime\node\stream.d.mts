import type nodeStream from "node:stream";
import promises from "node:stream/promises";
export { promises };
export { Readable } from "./internal/stream/readable.mjs";
export { Writable } from "./internal/stream/writable.mjs";
export { Duplex } from "./internal/stream/duplex.mjs";
export { Transform } from "./internal/stream/transform.mjs";
export declare const Stream: nodeStream.Stream;
export declare const PassThrough: nodeStream.PassThrough;
export declare const pipeline: any;
export declare const finished: any;
export declare const addAbortSignal: unknown;
export declare const isDisturbed: unknown;
export declare const isReadable: unknown;
export declare const compose: unknown;
export declare const isErrored: unknown;
export declare const destroy: unknown;
export declare const _isUint8Array: unknown;
export declare const _uint8ArrayToBuffer: unknown;
export declare const _isArrayBufferView: unknown;
export declare const duplexPair: unknown;
export declare const getDefaultHighWaterMark: unknown;
export declare const isDestroyed: unknown;
export declare const isWritable: unknown;
export declare const setDefaultHighWaterMark: unknown;
declare const _default: typeof nodeStream & {
	isDisturbed: any
	isReadable: any
	compose: any
	isErrored: any
	destroy: any
	_isUint8Array: any
	_uint8ArrayToBuffer: any
	_isArrayBufferView: any
	duplexPair: any
	getDefaultHighWaterMark: any
	isDestroyed: any
	isWritable: any
	setDefaultHighWaterMark: any
};
export default _default;
