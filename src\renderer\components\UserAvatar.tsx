import React from 'react';
import { User } from '../types';
import VoiceActivityIndicator from './VoiceActivityIndicator';

interface UserAvatarProps {
  user: User;
  size?: 'small' | 'medium' | 'large';
  showStatus?: boolean;
  showVoiceActivity?: boolean;
  className?: string;
  onClick?: () => void;
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'medium',
  showStatus = true,
  showVoiceActivity = true,
  className = '',
  onClick,
}) => {
  const sizeClasses = {
    small: 'w-6 h-6 text-xs',
    medium: 'w-8 h-8 text-sm',
    large: 'w-12 h-12 text-lg',
  };

  const statusSizeClasses = {
    small: 'w-2 h-2 -bottom-0.5 -right-0.5',
    medium: 'w-3 h-3 -bottom-0.5 -right-0.5',
    large: 'w-4 h-4 -bottom-1 -right-1',
  };

  const voiceActivitySizeClasses = {
    small: 'w-2 h-2 -top-0.5 -right-0.5',
    medium: 'w-3 h-3 -top-0.5 -right-0.5',
    large: 'w-4 h-4 -top-1 -right-1',
  };

  const getStatusColor = () => {
    if (!user.isConnected) return 'bg-discord-gray-500';
    if (user.isDeafened) return 'bg-discord-red';
    if (user.isMuted) return 'bg-discord-yellow';
    return 'bg-discord-green';
  };

  const getInitials = () => {
    return user.nickname
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div 
      className={`relative inline-block ${className} ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {/* 头像 */}
      <div 
        className={`
          ${sizeClasses[size]} 
          bg-discord-blurple 
          rounded-full 
          flex 
          items-center 
          justify-center 
          font-semibold 
          text-white
          transition-transform
          duration-200
          ${onClick ? 'hover:scale-105' : ''}
        `}
      >
        {getInitials()}
      </div>

      {/* 在线状态指示器 */}
      {showStatus && (
        <div 
          className={`
            absolute 
            ${statusSizeClasses[size]} 
            ${getStatusColor()} 
            rounded-full 
            border-2 
            border-discord-gray-800
          `}
          title={
            !user.isConnected 
              ? '离线' 
              : user.isDeafened 
                ? '已关闭声音' 
                : user.isMuted 
                  ? '已静音' 
                  : '在线'
          }
        />
      )}

      {/* 语音活动指示器 */}
      {showVoiceActivity && user.isConnected && user.isSpeaking && (
        <div className={`absolute ${voiceActivitySizeClasses[size]}`}>
          <VoiceActivityIndicator
            isSpeaking={user.isSpeaking}
            volume={user.volume}
            size={size === 'large' ? 'medium' : 'small'}
          />
        </div>
      )}

      {/* 静音图标覆盖 */}
      {user.isMuted && (
        <div 
          className={`
            absolute 
            inset-0 
            flex 
            items-center 
            justify-center 
            bg-black 
            bg-opacity-50 
            rounded-full
          `}
        >
          <svg 
            className={`
              ${size === 'small' ? 'w-3 h-3' : size === 'medium' ? 'w-4 h-4' : 'w-5 h-5'} 
              text-white
            `} 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path 
              fillRule="evenodd" 
              d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default UserAvatar;
