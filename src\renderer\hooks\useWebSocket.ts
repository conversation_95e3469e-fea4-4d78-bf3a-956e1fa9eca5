import { useState, useEffect, useRef, useCallback } from 'react';
import { User, Channel, AppEvent } from '../types';

interface UseWebSocketProps {
  user: User | null;
  onEvent: (event: AppEvent) => void;
}

interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

export function useWebSocket({ user, onEvent }: UseWebSocketProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 3000;

  const handleMessage = useCallback((message: WebSocketMessage) => {
    console.log('WebSocket message received:', message);
    switch (message.type) {
      case 'ROOM_EVENT':
        console.log('处理ROOM_EVENT消息:', message.payload);
        if (message.payload.type === 'USER_JOIN') {
          onEvent({
            type: 'USER_JOIN',
            payload: message.payload.payload
          });
        } else if (message.payload.type === 'USER_LEAVE') {
          onEvent({
            type: 'USER_LEAVE',
            payload: message.payload.payload
          });
        } else if (message.payload.type === 'USER_JOIN_CHANNEL') {
          onEvent({
            type: 'USER_JOIN_CHANNEL',
            payload: message.payload.payload
          });
        } else if (message.payload.type === 'USER_LEAVE_CHANNEL') {
          onEvent({
            type: 'USER_LEAVE_CHANNEL',
            payload: message.payload.payload
          });
        } else {
          onEvent(message.payload);
        }
        break;
      case 'ROOM_STATE':
        onEvent({
          type: 'ROOM_STATE_UPDATE',
          payload: message.payload
        });
        break;
      case 'USER_JOIN':
        onEvent({
          type: 'USER_JOIN',
          payload: message.payload
        });
        break;
      case 'USER_LEAVE':
        onEvent({
          type: 'USER_LEAVE',
          payload: message.payload
        });
        break;
      case 'USER_LIST_UPDATE':
        onEvent({
          type: 'USER_LIST_UPDATE',
          payload: message.payload
        });
        break;
      case 'CHANNEL_LIST_UPDATE':
        onEvent({
          type: 'CHANNEL_LIST_UPDATE',
          payload: message.payload
        });
        break;
      case 'USER_JOIN_CHANNEL':
        onEvent({
          type: 'USER_JOIN_CHANNEL',
          payload: message.payload
        });
        break;
      case 'USER_LEAVE_CHANNEL':
        onEvent({
          type: 'USER_LEAVE_CHANNEL',
          payload: message.payload
        });
        break;
      case 'ERROR':
        setError(message.payload.error);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }, [onEvent]);

  const send = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }, []);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (wsRef.current) {
      const ws = wsRef.current;
      wsRef.current = null; // Clear reference first
      ws.onclose = null; // Prevent recursive onclose calls
      ws.close();
    }
    setIsConnected(false);
    console.log("WebSocket disconnected by client.");
  }, []);

  const connect = useCallback(() => {
    if (!user || (wsRef.current && wsRef.current.readyState === WebSocket.OPEN)) {
      return;
    }

    disconnect(); // Disconnect any existing connection first

    try {
      const wsUrl = process.env.NODE_ENV === 'development'
        ? 'ws://localhost:8787/api/room/ws'
        : 'wss://your-worker.your-subdomain.workers.dev/api/room/ws';

      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setError(null);
        reconnectAttemptsRef.current = 0; // Reset on successful connection

        if (user) {
          send({
            type: 'JOIN_ROOM',
            payload: { user },
            timestamp: new Date().toISOString(),
            userId: user.id,
          });
        }
      };
      
      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };
      
      ws.onclose = (event) => {
        if (wsRef.current !== ws) return; // Ignore if this is not the current connection

        console.log(`WebSocket disconnected: ${event.code}. Reconnecting...`);
        setIsConnected(false);
        wsRef.current = null;

        if (user && reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          const delay = reconnectDelay * Math.pow(2, reconnectAttemptsRef.current - 1);
          console.log(`Reconnection attempt ${reconnectAttemptsRef.current} in ${delay}ms`);
          reconnectTimeoutRef.current = setTimeout(connect, delay);
        } else {
          setError('Connection failed after multiple attempts.');
          console.error('WebSocket: Max reconnect attempts reached.');
        }
      };
      
      ws.onerror = (err) => {
        console.error('WebSocket error:', err);
        setError('WebSocket connection error.');
      };
      
    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError('Failed to connect to server.');
    }
  }, [user, handleMessage, send, disconnect]);

  useEffect(() => {
    if (user) {
      reconnectAttemptsRef.current = 0;
      connect();
    }

    return () => {
      disconnect();
    };
  }, [user?.id, connect, disconnect]);

  // 加入频道
  const joinChannel = useCallback((channelId: string) => {
    if (user) {
      // 确保用户已经连接且加入了房间
      if (!isConnected) {
        console.warn('WebSocket未连接，尝试重新连接后加入频道');
        connect();
        // 连接后延迟发送加入频道消息
        setTimeout(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            console.log(`尝试加入频道 ${channelId}`);
            send({
              type: 'JOIN_CHANNEL',
              payload: { userId: user.id, channelId, user },
              timestamp: new Date().toISOString(),
              userId: user.id,
            });
          }
        }, 1000); // 给连接一些时间
        return;
      }
      
      console.log(`加入频道 ${channelId}，用户ID: ${user.id}`);
      send({
        type: 'JOIN_CHANNEL',
        payload: { userId: user.id, channelId, user },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    } else {
      console.warn('无法加入频道：用户未登录');
    }
  }, [user, send, isConnected, connect]);

  // 离开频道
  const leaveChannel = useCallback((channelId: string) => {
    if (user) {
      send({
        type: 'LEAVE_CHANNEL',
        payload: { userId: user.id, channelId },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 创建频道
  const createChannel = useCallback((name: string, type: 'voice' | 'text' = 'voice') => {
    if (user) {
      send({
        type: 'CREATE_CHANNEL',
        payload: { name, type, createdBy: user.id },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 删除频道
  const deleteChannel = useCallback((channelId: string) => {
    if (user) {
      send({
        type: 'DELETE_CHANNEL',
        payload: { channelId, deletedBy: user.id },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 重命名频道
  const renameChannel = useCallback((channelId: string, newName: string) => {
    if (user) {
      send({
        type: 'RENAME_CHANNEL',
        payload: { channelId, newName, renamedBy: user.id },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 更新用户状态
  const updateUserState = useCallback((updates: Partial<User>) => {
    if (user) {
      send({
        type: 'UPDATE_USER',
        payload: { userId: user.id, updates },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 发送WebRTC信令
  const sendSignaling = useCallback((signaling: any) => {
    if (user) {
      send({
        type: 'SIGNALING',
        payload: signaling,
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 手动重连
  const reconnect = useCallback(() => {
    reconnectAttemptsRef.current = 0;
    setError(null);
    connect();
  }, [connect]);

  return {
    isConnected,
    error,
    reconnectAttempts: reconnectAttemptsRef.current,
    send,
    joinChannel,
    leaveChannel,
    createChannel,
    deleteChannel,
    renameChannel,
    updateUserState,
    sendSignaling,
    reconnect,
    disconnect,
  };
}