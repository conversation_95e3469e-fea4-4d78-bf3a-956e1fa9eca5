import { useState, useEffect, useRef, useCallback } from 'react';
import { User, Channel, AppEvent } from '../types';

interface UseWebSocketProps {
  user: User | null;
  onEvent: (event: AppEvent) => void;
}

interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

export function useWebSocket({ user, onEvent }: UseWebSocketProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 3000;

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'ROOM_EVENT':
        onEvent(message.payload);
        break;
      case 'USER_LIST_UPDATE':
        break;
      case 'CHANNEL_LIST_UPDATE':
        break;
      case 'ERROR':
        setError(message.payload.error);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }, [onEvent]);

  const send = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }, []);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (wsRef.current) {
      wsRef.current.onclose = null; // Prevent recursive onclose calls
      wsRef.current.close();
      wsRef.current = null;
    }
    setIsConnected(false);
    console.log("WebSocket disconnected by client.");
  }, []);

  const connect = useCallback(() => {
    if (!user || (wsRef.current && wsRef.current.readyState === WebSocket.OPEN)) {
      return;
    }

    disconnect(); // Disconnect any existing connection first

    try {
      const wsUrl = process.env.NODE_ENV === 'development'
        ? 'ws://localhost:8787/api/room/ws'
        : 'wss://your-worker.your-subdomain.workers.dev/api/room/ws';

      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setError(null);
        reconnectAttemptsRef.current = 0; // Reset on successful connection

        if (user) {
          send({
            type: 'JOIN_ROOM',
            payload: { user },
            timestamp: new Date().toISOString(),
            userId: user.id,
          });
        }
      };
      
      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };
      
      ws.onclose = (event) => {
        if (!wsRef.current) return; // Ignore if intentionally disconnected

        console.log(`WebSocket disconnected: ${event.code}. Reconnecting...`);
        setIsConnected(false);
        wsRef.current = null;
        
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          const delay = reconnectDelay * Math.pow(2, reconnectAttemptsRef.current - 1);
          console.log(`Reconnection attempt ${reconnectAttemptsRef.current} in ${delay}ms`);
          reconnectTimeoutRef.current = setTimeout(connect, delay);
        } else {
          setError('Connection failed after multiple attempts.');
          console.error('WebSocket: Max reconnect attempts reached.');
        }
      };
      
      ws.onerror = (err) => {
        console.error('WebSocket error:', err);
        setError('WebSocket connection error.');
      };
      
    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError('Failed to connect to server.');
    }
  }, [user, handleMessage, send, disconnect]);

  useEffect(() => {
    if (user) {
      reconnectAttemptsRef.current = 0;
      connect();
    } else {
      disconnect();
    }
    
    return () => {
      disconnect();
    };
  }, [user, connect, disconnect]);

  // 加入频道
  const joinChannel = useCallback((channelId: string) => {
    if (user) {
      send({
        type: 'JOIN_CHANNEL',
        payload: { userId: user.id, channelId },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 离开频道
  const leaveChannel = useCallback((channelId: string) => {
    if (user) {
      send({
        type: 'LEAVE_CHANNEL',
        payload: { userId: user.id, channelId },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 创建频道
  const createChannel = useCallback((name: string, type: 'voice' | 'text' = 'voice') => {
    if (user) {
      send({
        type: 'CREATE_CHANNEL',
        payload: { name, type, createdBy: user.id },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 删除频道
  const deleteChannel = useCallback((channelId: string) => {
    if (user) {
      send({
        type: 'DELETE_CHANNEL',
        payload: { channelId, deletedBy: user.id },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 重命名频道
  const renameChannel = useCallback((channelId: string, newName: string) => {
    if (user) {
      send({
        type: 'RENAME_CHANNEL',
        payload: { channelId, newName, renamedBy: user.id },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 更新用户状态
  const updateUserState = useCallback((updates: Partial<User>) => {
    if (user) {
      send({
        type: 'UPDATE_USER',
        payload: { userId: user.id, updates },
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 发送WebRTC信令
  const sendSignaling = useCallback((signaling: any) => {
    if (user) {
      send({
        type: 'SIGNALING',
        payload: signaling,
        timestamp: new Date().toISOString(),
        userId: user.id,
      });
    }
  }, [user, send]);

  // 手动重连
  const reconnect = useCallback(() => {
    reconnectAttemptsRef.current = 0;
    setError(null);
    connect();
  }, [connect]);

  return {
    isConnected,
    error,
    reconnectAttempts: reconnectAttemptsRef.current,
    send,
    joinChannel,
    leaveChannel,
    createChannel,
    deleteChannel,
    renameChannel,
    updateUserState,
    sendSignaling,
    reconnect,
    disconnect,
  };
}