import { useState, useEffect, useRef, useCallback } from 'react';
import { User, Channel, AppEvent } from '../types';

interface UseWebSocketProps {
  user: User;
  onEvent: (event: AppEvent) => void;
}

interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

export function useWebSocket({ user, onEvent }: UseWebSocketProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 3000;

  // 连接WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      // 开发环境URL - 需要根据实际部署调整
      const wsUrl = process.env.NODE_ENV === 'development' 
        ? 'ws://localhost:8787/api/room/ws'
        : 'wss://your-worker.your-subdomain.workers.dev/api/room/ws';
      
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setError(null);
        setReconnectAttempts(0);
        wsRef.current = ws;
        
        // 发送用户加入消息
        send({
          type: 'JOIN_ROOM',
          payload: { user },
          timestamp: new Date().toISOString(),
          userId: user.id,
        });
      };
      
      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };
      
      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        wsRef.current = null;
        
        // 自动重连
        if (reconnectAttempts < maxReconnectAttempts) {
          setReconnectAttempts(prev => prev + 1);
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectDelay * Math.pow(2, reconnectAttempts)); // 指数退避
        } else {
          setError('Connection failed after multiple attempts');
        }
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setError('WebSocket connection error');
      };
      
    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError('Failed to connect to server');
    }
  }, [user, reconnectAttempts]);

  // 处理接收到的消息
  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'ROOM_EVENT':
        onEvent(message.payload);
        break;
      case 'USER_LIST_UPDATE':
        // 处理用户列表更新
        break;
      case 'CHANNEL_LIST_UPDATE':
        // 处理频道列表更新
        break;
      case 'ERROR':
        setError(message.payload.error);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }, [onEvent]);

  // 发送消息
  const send = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }, []);

  // 加入频道
  const joinChannel = useCallback((channelId: string) => {
    send({
      type: 'JOIN_CHANNEL',
      payload: { userId: user.id, channelId },
      timestamp: new Date().toISOString(),
      userId: user.id,
    });
  }, [user.id, send]);

  // 离开频道
  const leaveChannel = useCallback((channelId: string) => {
    send({
      type: 'LEAVE_CHANNEL',
      payload: { userId: user.id, channelId },
      timestamp: new Date().toISOString(),
      userId: user.id,
    });
  }, [user.id, send]);

  // 创建频道
  const createChannel = useCallback((name: string, type: 'voice' | 'text' = 'voice') => {
    send({
      type: 'CREATE_CHANNEL',
      payload: { name, type, createdBy: user.id },
      timestamp: new Date().toISOString(),
      userId: user.id,
    });
  }, [user.id, send]);

  // 删除频道
  const deleteChannel = useCallback((channelId: string) => {
    send({
      type: 'DELETE_CHANNEL',
      payload: { channelId, deletedBy: user.id },
      timestamp: new Date().toISOString(),
      userId: user.id,
    });
  }, [user.id, send]);

  // 重命名频道
  const renameChannel = useCallback((channelId: string, newName: string) => {
    send({
      type: 'RENAME_CHANNEL',
      payload: { channelId, newName, renamedBy: user.id },
      timestamp: new Date().toISOString(),
      userId: user.id,
    });
  }, [user.id, send]);

  // 更新用户状态
  const updateUserState = useCallback((updates: Partial<User>) => {
    send({
      type: 'UPDATE_USER',
      payload: { userId: user.id, updates },
      timestamp: new Date().toISOString(),
      userId: user.id,
    });
  }, [user.id, send]);

  // 发送WebRTC信令
  const sendSignaling = useCallback((signaling: any) => {
    send({
      type: 'SIGNALING',
      payload: signaling,
      timestamp: new Date().toISOString(),
      userId: user.id,
    });
  }, [user.id, send]);

  // 手动重连
  const reconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    setReconnectAttempts(0);
    setError(null);
    connect();
  }, [connect]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    if (wsRef.current) {
      wsRef.current.close();
    }
    setIsConnected(false);
    setReconnectAttempts(maxReconnectAttempts); // 防止自动重连
  }, []);

  // 初始化连接
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    isConnected,
    error,
    reconnectAttempts,
    send,
    joinChannel,
    leaveChannel,
    createChannel,
    deleteChannel,
    renameChannel,
    updateUserState,
    sendSignaling,
    reconnect,
    disconnect,
  };
}
