// 用户类型
export interface User {
  id: string;
  nickname: string;
  isConnected: boolean;
  isMuted: boolean;
  isDeafened: boolean;
  isSpeaking: boolean;
  volume: number;
  joinedAt: Date;
}

// 频道类型
export interface Channel {
  id: string;
  name: string;
  type: 'voice' | 'text';
  userCount: number;
  users: User[];
  createdAt: Date;
  updatedAt: Date;
}

// 房间类型
export interface Room {
  id: string;
  name: string;
  channels: Channel[];
  users: User[];
  createdAt: Date;
  updatedAt: Date;
}

// WebRTC相关类型
export interface PeerConnection {
  id: string;
  userId: string;
  connection: RTCPeerConnection;
  stream?: MediaStream;
}

// 语音状态类型
export interface VoiceState {
  isConnected: boolean;
  isMuted: boolean;
  isDeafened: boolean;
  currentChannelId?: string;
  volume: number;
  inputDevice?: string;
  outputDevice?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Cloudflare Calls API相关类型
export interface CallsSession {
  sessionId: string;
  appId: string;
  tracks: CallsTrack[];
}

export interface CallsTrack {
  trackName: string;
  mid: string;
  location: 'local' | 'remote';
  sessionId?: string;
  status: 'active' | 'inactive' | 'waiting';
}

export interface SessionDescription {
  sdp: string;
  type: 'offer' | 'answer';
}

// 应用状态类型
export interface AppState {
  user: User | null;
  room: Room | null;
  currentChannel: Channel | null;
  voiceState: VoiceState;
  peers: PeerConnection[];
  isLoading: boolean;
  error: string | null;
}

// 事件类型
export type AppEvent =
  | { type: 'USER_JOIN'; payload: User }
  | { type: 'USER_LEAVE'; payload: { userId: string } }
  | { type: 'USER_UPDATE'; payload: User }
  | { type: 'USER_LIST_UPDATE'; payload: User[] }
  | { type: 'USER_JOIN_CHANNEL'; payload: { userId: string, channelId: string, user: User } }
  | { type: 'USER_LEAVE_CHANNEL'; payload: { userId: string, channelId: string } }
  | { type: 'CHANNEL_CREATE'; payload: Channel }
  | { type: 'CHANNEL_DELETE'; payload: { channelId: string } }
  | { type: 'CHANNEL_UPDATE'; payload: Channel }
  | { type: 'CHANNEL_LIST_UPDATE'; payload: Channel[] }
  | { type: 'ROOM_STATE_UPDATE'; payload: Room }
  | { type: 'VOICE_STATE_UPDATE'; payload: Partial<VoiceState> }
  | { type: 'PEER_CONNECT'; payload: PeerConnection }
  | { type: 'PEER_DISCONNECT'; payload: { peerId: string } }
  | { type: 'ERROR'; payload: { error: string } };
