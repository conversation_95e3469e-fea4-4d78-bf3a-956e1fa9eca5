import React, { useEffect, useState } from 'react';

interface VoiceActivityIndicatorProps {
  isSpeaking: boolean;
  volume?: number;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const VoiceActivityIndicator: React.FC<VoiceActivityIndicatorProps> = ({
  isSpeaking,
  volume = 0,
  size = 'medium',
  className = '',
}) => {
  const [animationLevel, setAnimationLevel] = useState(0);

  useEffect(() => {
    if (isSpeaking) {
      // 根据音量设置动画级别
      const level = Math.min(Math.max(volume / 100, 0.3), 1);
      setAnimationLevel(level);
    } else {
      setAnimationLevel(0);
    }
  }, [isSpeaking, volume]);

  const sizeClasses = {
    small: 'w-2 h-2',
    medium: 'w-3 h-3',
    large: 'w-4 h-4',
  };

  const baseClasses = `${sizeClasses[size]} rounded-full transition-all duration-150`;

  if (!isSpeaking) {
    return (
      <div className={`${baseClasses} bg-discord-gray-500 ${className}`} />
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* 主指示器 */}
      <div 
        className={`${baseClasses} bg-discord-green`}
        style={{
          transform: `scale(${0.8 + animationLevel * 0.4})`,
          boxShadow: `0 0 ${animationLevel * 8}px rgba(87, 242, 135, ${animationLevel * 0.6})`,
        }}
      />
      
      {/* 脉冲效果 */}
      <div 
        className={`absolute inset-0 ${baseClasses} bg-discord-green animate-ping`}
        style={{
          opacity: animationLevel * 0.4,
          animationDuration: `${1.5 - animationLevel * 0.5}s`,
        }}
      />
    </div>
  );
};

export default VoiceActivityIndicator;
