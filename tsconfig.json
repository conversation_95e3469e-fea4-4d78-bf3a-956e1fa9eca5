{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "baseUrl": ".", "outDir": "./dist", "paths": {"@/*": ["src/*"], "@/components/*": ["src/renderer/components/*"], "@/hooks/*": ["src/renderer/hooks/*"], "@/utils/*": ["src/renderer/utils/*"], "@/types/*": ["src/renderer/types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}