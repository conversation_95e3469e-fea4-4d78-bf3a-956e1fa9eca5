import { CallsSession, CallsTrack, SessionDescription, ApiResponse } from '../types';

class CallsApiClient {
  private baseUrl: string;

  constructor() {
    // 根据环境设置API基础URL
    this.baseUrl = process.env.NODE_ENV === 'development'
      ? 'http://localhost:8787/api/calls'
      : 'https://your-worker.your-subdomain.workers.dev/api/calls';
  }

  // 创建新的WebRTC会话
  async createSession(correlationId?: string): Promise<ApiResponse<CallsSession>> {
    try {
      const params = new URLSearchParams();
      if (correlationId) {
        params.append('correlationId', correlationId);
      }

      const response = await fetch(`${this.baseUrl}/sessions/new?${params.toString()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ correlationId }),
      });

      return await response.json();
    } catch (error) {
      console.error('Create session error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // 添加音轨到会话
  async addTracks(
    sessionId: string,
    tracks: any[],
    sessionDescription?: SessionDescription
  ): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/tracks/add?sessionId=${sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tracks,
          sessionDescription,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Add tracks error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // 重新协商WebRTC会话
  async renegotiate(
    sessionId: string,
    sessionDescription: SessionDescription
  ): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/renegotiate?sessionId=${sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionDescription }),
      });

      return await response.json();
    } catch (error) {
      console.error('Renegotiate error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // 关闭音轨
  async closeTracks(
    sessionId: string,
    tracks: any[],
    sessionDescription?: SessionDescription,
    force?: boolean
  ): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/tracks/close?sessionId=${sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tracks,
          sessionDescription,
          force,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Close tracks error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // 更新音轨
  async updateTracks(sessionId: string, tracks: any[]): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/tracks/update?sessionId=${sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tracks }),
      });

      return await response.json();
    } catch (error) {
      console.error('Update tracks error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // 获取会话状态
  async getSessionState(sessionId: string): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/session/state?sessionId=${sessionId}`, {
        method: 'GET',
      });

      return await response.json();
    } catch (error) {
      console.error('Get session state error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

// 创建单例实例
export const callsApi = new CallsApiClient();

// WebRTC工具函数
export class WebRTCManager {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private sessionId: string | null = null;

  constructor() {
    this.initializePeerConnection();
  }

  private initializePeerConnection() {
    const config: RTCConfiguration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
    };

    this.peerConnection = new RTCPeerConnection(config);

    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        // 发送ICE候选到信令服务器
        this.sendIceCandidate(event.candidate);
      }
    };

    this.peerConnection.ontrack = (event) => {
      // 处理远程音轨
      const [remoteStream] = event.streams;
      this.handleRemoteStream(remoteStream);
    };

    this.peerConnection.onconnectionstatechange = () => {
      console.log('Connection state:', this.peerConnection?.connectionState);
    };
  }

  // 获取本地媒体流
  async getLocalStream(): Promise<MediaStream> {
    if (this.localStream) {
      return this.localStream;
    }

    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
        video: false,
      });

      // 添加音轨到PeerConnection
      this.localStream.getTracks().forEach(track => {
        if (this.peerConnection && this.localStream) {
          this.peerConnection.addTrack(track, this.localStream);
        }
      });

      return this.localStream;
    } catch (error) {
      console.error('Failed to get local stream:', error);
      throw error;
    }
  }

  // 创建Offer
  async createOffer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection not initialized');
    }

    const offer = await this.peerConnection.createOffer();
    await this.peerConnection.setLocalDescription(offer);
    return offer;
  }

  // 创建Answer
  async createAnswer(offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection not initialized');
    }

    await this.peerConnection.setRemoteDescription(offer);
    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);
    return answer;
  }

  // 设置远程描述
  async setRemoteDescription(description: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection not initialized');
    }

    await this.peerConnection.setRemoteDescription(description);
  }

  // 添加ICE候选
  async addIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection not initialized');
    }

    await this.peerConnection.addIceCandidate(candidate);
  }

  // 静音/取消静音
  setMuted(muted: boolean): void {
    if (this.localStream) {
      this.localStream.getAudioTracks().forEach(track => {
        track.enabled = !muted;
      });
    }
  }

  // 设置音量
  setVolume(volume: number): void {
    // 这里可以实现音量控制逻辑
    // 注意：Web Audio API可能需要用于更精确的音量控制
  }

  // 关闭连接
  close(): void {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
  }

  // 发送ICE候选（需要实现信令逻辑）
  private sendIceCandidate(candidate: RTCIceCandidate): void {
    // 这里应该通过WebSocket发送ICE候选
    console.log('ICE candidate:', candidate);
  }

  // 处理远程流
  private handleRemoteStream(stream: MediaStream): void {
    // 这里处理远程音频流
    console.log('Remote stream received:', stream);
  }
}

// 创建WebRTC管理器实例
export const webrtcManager = new WebRTCManager();
