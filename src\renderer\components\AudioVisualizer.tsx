import React, { useEffect, useRef } from 'react';

interface AudioVisualizerProps {
  audioData?: Uint8Array | null;
  isActive?: boolean;
  width?: number;
  height?: number;
  barCount?: number;
  className?: string;
  color?: string;
}

const AudioVisualizer: React.FC<AudioVisualizerProps> = ({
  audioData,
  isActive = false,
  width = 100,
  height = 20,
  barCount = 8,
  className = '',
  color = '#57f287', // Discord green
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(undefined);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const draw = () => {
      // 清空画布
      ctx.clearRect(0, 0, width, height);

      if (!isActive) {
        // 如果不活跃，绘制静态条
        drawStaticBars(ctx);
      } else if (audioData) {
        // 如果有音频数据，绘制动态条
        drawAudioBars(ctx, audioData);
      } else {
        // 如果活跃但没有数据，绘制动画条
        drawAnimatedBars(ctx);
      }

      if (isActive) {
        animationRef.current = requestAnimationFrame(() => draw());
      }
    };

    draw();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [audioData, isActive, width, height, barCount, color]);

  const drawStaticBars = (ctx: CanvasRenderingContext2D) => {
    const barWidth = width / barCount;
    const minHeight = 2;

    ctx.fillStyle = '#4f5660'; // 灰色

    for (let i = 0; i < barCount; i++) {
      const x = i * barWidth + barWidth * 0.1;
      const barHeight = minHeight;
      const y = height - barHeight;

      ctx.fillRect(x, y, barWidth * 0.8, barHeight);
    }
  };

  const drawAudioBars = (ctx: CanvasRenderingContext2D, data: Uint8Array) => {
    const barWidth = width / barCount;
    const minHeight = 2;

    ctx.fillStyle = color;

    for (let i = 0; i < barCount; i++) {
      // 从音频数据中采样
      const dataIndex = Math.floor((i / barCount) * data.length);
      const amplitude = data[dataIndex] / 255;
      
      const barHeight = Math.max(minHeight, amplitude * height);
      const x = i * barWidth + barWidth * 0.1;
      const y = height - barHeight;

      ctx.fillRect(x, y, barWidth * 0.8, barHeight);
    }
  };

  const drawAnimatedBars = (ctx: CanvasRenderingContext2D) => {
    const barWidth = width / barCount;
    const minHeight = 2;
    const time = Date.now() * 0.005;

    ctx.fillStyle = color;

    for (let i = 0; i < barCount; i++) {
      // 创建波浪动画
      const amplitude = (Math.sin(time + i * 0.5) + 1) * 0.5;
      const barHeight = Math.max(minHeight, amplitude * height * 0.8);
      const x = i * barWidth + barWidth * 0.1;
      const y = height - barHeight;

      ctx.fillRect(x, y, barWidth * 0.8, barHeight);
    }
  };

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      className={`${className}`}
      style={{ width: `${width}px`, height: `${height}px` }}
    />
  );
};

export default AudioVisualizer;
