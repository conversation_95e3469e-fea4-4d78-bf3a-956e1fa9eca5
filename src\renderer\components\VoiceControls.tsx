import React, { useState } from 'react';
import { User, VoiceState } from '../types';
import UserAvatar from './UserAvatar';
import AudioSettings from './AudioSettings';
import AudioVisualizer from './AudioVisualizer';
import { AudioDeviceManager } from '../utils/audioProcessor';

interface VoiceControlsProps {
  user: User;
  voiceState: VoiceState;
  onVoiceStateChange: (newState: Partial<VoiceState>) => void;
  webRTC?: {
    toggleMute: () => void;
    toggleDeafen?: () => void;
    setVolume: (volume: number) => void;
    isSpeaking: boolean;
    currentVolume: number;
    deviceManager: AudioDeviceManager;
  };
}

const VoiceControls: React.FC<VoiceControlsProps> = ({
  user,
  voiceState,
  onVoiceStateChange,
  webRTC,
}) => {
  const [showSettings, setShowSettings] = useState(false);

  const handleMuteToggle = () => {
    if (webRTC) {
      webRTC.toggleMute();
    } else {
      onVoiceStateChange({ isMuted: !voiceState.isMuted });
    }
  };

  const handleDeafenToggle = () => {
    if (webRTC?.toggleDeafen) {
      webRTC.toggleDeafen();
    } else {
      onVoiceStateChange({ isDeafened: !voiceState.isDeafened });
    }
  };

  const handleVolumeChange = (volume: number) => {
    if (webRTC) {
      webRTC.setVolume(volume);
    } else {
      onVoiceStateChange({ volume });
    }
  };

  const handleConnect = () => {
    onVoiceStateChange({ isConnected: !voiceState.isConnected });
  };

  return (
    <div className="h-16 bg-discord-gray-900 border-t border-discord-gray-700 px-4 flex items-center justify-between">
      {/* 左侧：用户信息和状态 */}
      <div className="flex items-center space-x-3">
        <UserAvatar
          user={{
            ...user,
            isConnected: voiceState.isConnected,
            isMuted: voiceState.isMuted,
            isDeafened: voiceState.isDeafened,
            isSpeaking: webRTC?.isSpeaking || false,
            volume: webRTC?.currentVolume || voiceState.volume,
          }}
          size="large"
          showStatus={true}
          showVoiceActivity={true}
        />

        <div>
          <div className="text-sm font-medium text-white">{user.nickname}</div>
          <div className="text-xs text-discord-gray-400">
            {voiceState.isConnected
              ? voiceState.currentChannelId
                ? '已连接到语音频道'
                : '已连接'
              : '未连接'
            }
          </div>
        </div>
      </div>

      {/* 中间：语音控制按钮 */}
      <div className="flex items-center space-x-2">
        {/* 静音按钮 */}
        <button
          onClick={handleMuteToggle}
          className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 ${
            voiceState.isMuted
              ? 'bg-discord-red hover:bg-red-600'
              : 'bg-discord-gray-700 hover:bg-discord-gray-600'
          }`}
          title={voiceState.isMuted ? '取消静音' : '静音'}
        >
          {voiceState.isMuted ? (
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.594-.471-3.078-1.343-4.243a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          )}
        </button>

        {/* 关闭声音按钮 */}
        <button
          onClick={handleDeafenToggle}
          className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 ${
            voiceState.isDeafened
              ? 'bg-discord-red hover:bg-red-600'
              : 'bg-discord-gray-700 hover:bg-discord-gray-600'
          }`}
          title={voiceState.isDeafened ? '开启声音' : '关闭声音'}
        >
          {voiceState.isDeafened ? (
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
              <path d="M3 3l14 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
            </svg>
          ) : (
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
            </svg>
          )}
        </button>

        {/* 连接/断开按钮 */}
        <button
          onClick={handleConnect}
          className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200 ${
            voiceState.isConnected
              ? 'bg-discord-red hover:bg-red-600'
              : 'bg-discord-green hover:bg-green-600'
          }`}
          title={voiceState.isConnected ? '断开连接' : '连接'}
        >
          {voiceState.isConnected ? (
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 6.707 6.293a1 1 0 00-1.414 1.414L8.586 11l-3.293 3.293a1 1 0 001.414 1.414L10 12.414l3.293 3.293a1 1 0 001.414-1.414L11.414 11l3.293-3.293z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
            </svg>
          )}
        </button>
      </div>

      {/* 右侧：设置和音量控制 */}
      <div className="flex items-center space-x-2">
        {/* 音量控制和可视化 */}
        <div className="flex items-center space-x-2">
          <svg className="w-4 h-4 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>

          {/* 音频可视化 */}
          <AudioVisualizer
            isActive={webRTC?.isSpeaking || false}
            width={60}
            height={12}
            barCount={6}
            color="#57f287"
            className="mx-2"
          />

          <input
            type="range"
            min="0"
            max="100"
            value={voiceState.volume}
            onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
            className="w-16 h-1 bg-discord-gray-700 rounded-lg appearance-none cursor-pointer slider"
          />
          <span className="text-xs text-discord-gray-400 w-8 text-right">
            {voiceState.volume}%
          </span>
        </div>

        {/* 设置按钮 */}
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="w-8 h-8 rounded flex items-center justify-center text-discord-gray-400 hover:text-gray-200 hover:bg-discord-gray-700 transition-colors duration-200"
          title="设置"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* 设置面板 */}
      {showSettings && webRTC?.deviceManager && (
        <div className="absolute bottom-16 right-4 w-96 z-50">
          <AudioSettings
            deviceManager={webRTC.deviceManager}
            currentVolume={webRTC.currentVolume}
            onVolumeChange={handleVolumeChange}
            onClose={() => setShowSettings(false)}
          />
        </div>
      )}
    </div>
  );
};

export default VoiceControls;
