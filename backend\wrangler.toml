name = "opz-backend"
main = "src/index.ts"
compatibility_date = "2024-06-01"

[dev]
port = 8787

[env.production]
name = "opz-backend-prod"

[env.development]
name = "opz-backend-dev"

# Durable Objects for state management
[[durable_objects.bindings]]
name = "ROOM_STATE"
class_name = "RoomState"

[[durable_objects.bindings]]
name = "USER_STATE"
class_name = "UserState"

# Environment variables
[vars]
CLOUDFLARE_CALLS_APP_ID = "3e89570cd30fb2bf5c6491e7d23d5efe"
CLOUDFLARE_CALLS_SECRET = "629af27dfbdafa35f412548e87a5a2bad2a376073a21bd7b7daeba5df85b0158"
CORS_ORIGIN = "*"

# KV for persistent storage (optional)
[[kv_namespaces]]
binding = "ROOM_KV"
id = "fe0b74432a584e7bb04971a5cdf99a74"
preview_id = "fe0b74432a584e7bb04971a5cdf99a74"

[[migrations]]
tag = "v1"
new_classes = [ "RoomState", "UserState" ]
