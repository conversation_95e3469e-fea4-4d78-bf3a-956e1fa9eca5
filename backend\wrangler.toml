name = "opz-backend"
main = "src/index.ts"
compatibility_date = "2024-06-01"

[env.production]
name = "opz-backend-prod"

[env.development]
name = "opz-backend-dev"

# Durable Objects for state management
[[durable_objects.bindings]]
name = "ROOM_STATE"
class_name = "RoomState"

[[durable_objects.bindings]]
name = "USER_STATE"
class_name = "UserState"

# Environment variables
[vars]
CLOUDFLARE_CALLS_APP_ID = "your-calls-app-id"
CLOUDFLARE_CALLS_SECRET = "your-calls-secret"
CORS_ORIGIN = "*"

# KV for persistent storage (optional)
[[kv_namespaces]]
binding = "ROOM_KV"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"
