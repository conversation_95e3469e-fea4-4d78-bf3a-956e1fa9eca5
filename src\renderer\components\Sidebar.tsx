import React, { useState } from 'react';
import { User, Channel } from '../types';
import ChannelManager from './ChannelManager';
import UserManager from './UserManager';

interface SidebarProps {
  user: User | null;
  users: User[];
  channels: Channel[];
  currentChannel: Channel | null;
  onChannelSelect: (channel: Channel) => void;
  onChannelCreate: (name: string, type?: 'voice' | 'text') => void;
  onChannelDelete: (channelId: string) => void;
  onChannelRename: (channelId: string, newName: string) => void;
  onUserUpdate: (updates: Partial<User>) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  user,
  users,
  channels,
  currentChannel,
  onChannelSelect,
  onChannelCreate,
  onChannelDelete,
  onChannelRename,
  onUserUpdate,
}) => {


  return (
    <div className="w-60 bg-discord-gray-800 flex flex-col">
      {/* 服务器标题 */}
      <div className="h-12 px-4 flex items-center border-b border-discord-gray-700">
        <h1 className="text-white font-semibold">语音聊天室</h1>
      </div>

      {/* 频道列表 */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        <div className="p-2">
          <ChannelManager
            channels={channels}
            currentChannel={currentChannel}
            user={user}
            onChannelSelect={onChannelSelect}
            onChannelCreate={onChannelCreate}
            onChannelDelete={onChannelDelete}
            onChannelRename={onChannelRename}
          />
        </div>
      </div>

      {/* 用户信息 */}
      <div className="bg-discord-gray-900 px-2 py-2">
        <UserManager
          user={user}
          users={users}
          onUserUpdate={onUserUpdate}
        />
      </div>


    </div>
  );
};

export default Sidebar;
