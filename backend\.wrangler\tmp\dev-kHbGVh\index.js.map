{"version": 3, "sources": ["../bundle-uSIFVA/checked-fetch.js", "../../../src/utils/index.ts", "../../../src/handlers/RoomState.ts", "../../../src/handlers/UserState.ts", "../../../src/handlers/CallsHandler.ts", "../../../src/index.ts", "../../../../../../AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../../../../AppData/Roaming/npm/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-uSIFVA/middleware-insertion-facade.js", "../../../../../../AppData/Roaming/npm/node_modules/wrangler/templates/middleware/common.ts", "../bundle-uSIFVA/middleware-loader.entry.ts"], "sourceRoot": "C:\\Users\\<USER>\\Desktop\\opz\\backend\\.wrangler\\tmp\\dev-kHbGVh", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "import { ApiResponse } from '../types';\n\n// 生成UUID\nexport function generateId(): string {\n  return crypto.randomUUID();\n}\n\n// 生成随机昵称\nexport function generateRandomNickname(): string {\n  const adjectives = [\n    '快乐的', '聪明的', '勇敢的', '友善的', '神秘的', '活泼的', '冷静的', '幽默的',\n    '优雅的', '坚强的', '温柔的', '机智的', '热情的', '安静的', '开朗的', '谦逊的'\n  ];\n  \n  const nouns = [\n    '狐狸', '熊猫', '老虎', '狮子', '大象', '海豚', '企鹅', '猫咪',\n    '小狗', '兔子', '松鼠', '猴子', '鹦鹉', '蝴蝶', '独角兽', '龙'\n  ];\n  \n  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];\n  const noun = nouns[Math.floor(Math.random() * nouns.length)];\n  const number = Math.floor(Math.random() * 9999) + 1;\n  \n  return `${adjective}${noun}${number}`;\n}\n\n// 创建成功响应\nexport function createSuccessResponse<T>(data: T, message?: string): ApiResponse<T> {\n  return {\n    success: true,\n    data,\n    message\n  };\n}\n\n// 创建错误响应\nexport function createErrorResponse(error: string, message?: string): ApiResponse {\n  return {\n    success: false,\n    error,\n    message\n  };\n}\n\n// 创建JSON响应\nexport function createJsonResponse<T>(data: ApiResponse<T>, status: number = 200): Response {\n  return new Response(JSON.stringify(data), {\n    status,\n    headers: {\n      'Content-Type': 'application/json',\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n\n// 处理CORS预检请求\nexport function handleCorsOptions(): Response {\n  return new Response(null, {\n    status: 204,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n      'Access-Control-Max-Age': '86400',\n    },\n  });\n}\n\n// 解析请求体\nexport async function parseRequestBody<T>(request: Request): Promise<T | null> {\n  try {\n    const contentType = request.headers.get('content-type');\n    if (contentType?.includes('application/json')) {\n      return await request.json();\n    }\n    return null;\n  } catch (error) {\n    console.error('Failed to parse request body:', error);\n    return null;\n  }\n}\n\n// 验证必需字段\nexport function validateRequiredFields(data: any, fields: string[]): string | null {\n  for (const field of fields) {\n    if (!data || data[field] === undefined || data[field] === null) {\n      return `Missing required field: ${field}`;\n    }\n  }\n  return null;\n}\n\n// 格式化时间戳\nexport function formatTimestamp(date: Date = new Date()): string {\n  return date.toISOString();\n}\n\n// 清理过期连接\nexport function isConnectionExpired(lastActivity: string, timeoutMinutes: number = 30): boolean {\n  const lastActivityTime = new Date(lastActivity).getTime();\n  const now = Date.now();\n  const timeoutMs = timeoutMinutes * 60 * 1000;\n  return (now - lastActivityTime) > timeoutMs;\n}\n\n// 广播消息到所有连接\nexport function broadcastToConnections(connections: Map<string, WebSocket>, message: any, excludeId?: string): void {\n  const messageStr = JSON.stringify(message);\n  \n  for (const [connectionId, ws] of connections.entries()) {\n    if (excludeId && connectionId === excludeId) {\n      continue;\n    }\n    \n    try {\n      if (ws.readyState === WebSocket.READY_STATE_OPEN) {\n        ws.send(messageStr);\n      } else {\n        // 清理已关闭的连接\n        connections.delete(connectionId);\n      }\n    } catch (error) {\n      console.error(`Failed to send message to connection ${connectionId}:`, error);\n      connections.delete(connectionId);\n    }\n  }\n}\n\n// 创建WebSocket响应\nexport function createWebSocketResponse(webSocket: WebSocket): Response {\n  return new Response(null, {\n    status: 101,\n    webSocket,\n  });\n}\n\n// 错误处理包装器\nexport function withErrorHandling<T extends any[], R>(\n  fn: (...args: T) => Promise<R>\n): (...args: T) => Promise<R | Response> {\n  return async (...args: T): Promise<R | Response> => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      console.error('Handler error:', error);\n      return createJsonResponse(\n        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  };\n}\n\n// 限流工具\nexport class RateLimiter {\n  private requests: Map<string, number[]> = new Map();\n  \n  constructor(\n    private maxRequests: number = 100,\n    private windowMs: number = 60000 // 1分钟\n  ) {}\n  \n  isAllowed(identifier: string): boolean {\n    const now = Date.now();\n    const windowStart = now - this.windowMs;\n    \n    // 获取或创建请求记录\n    let requests = this.requests.get(identifier) || [];\n    \n    // 清理过期请求\n    requests = requests.filter(timestamp => timestamp > windowStart);\n    \n    // 检查是否超过限制\n    if (requests.length >= this.maxRequests) {\n      return false;\n    }\n    \n    // 添加当前请求\n    requests.push(now);\n    this.requests.set(identifier, requests);\n    \n    return true;\n  }\n  \n  // 清理过期数据\n  cleanup(): void {\n    const now = Date.now();\n    const windowStart = now - this.windowMs;\n    \n    for (const [identifier, requests] of this.requests.entries()) {\n      const validRequests = requests.filter(timestamp => timestamp > windowStart);\n      if (validRequests.length === 0) {\n        this.requests.delete(identifier);\n      } else {\n        this.requests.set(identifier, validRequests);\n      }\n    }\n  }\n}\n", "import { \n  Room, \n  Channel, \n  User, \n  RoomEvent, \n  WebSocketMessage, \n  SignalingMessage,\n  RoomStateData \n} from '../types';\nimport { \n  generateId, \n  formatTimestamp, \n  broadcastToConnections,\n  createWebSocketResponse \n} from '../utils';\n\nexport class RoomState implements DurableObject {\n  private room: Room;\n  private connections: Map<string, WebSocket> = new Map();\n  private lastActivity: string = formatTimestamp();\n\n  constructor(private state: DurableObjectState, private env: any) {\n    // 初始化默认房间\n    this.room = {\n      id: 'default-room',\n      name: '语音聊天室',\n      channels: [\n        {\n          id: generateId(),\n          name: '大厅',\n          type: 'voice',\n          userCount: 0,\n          users: [],\n          createdAt: formatTimestamp(),\n          updatedAt: formatTimestamp(),\n        },\n        {\n          id: generateId(),\n          name: '游戏频道',\n          type: 'voice',\n          userCount: 0,\n          users: [],\n          createdAt: formatTimestamp(),\n          updatedAt: formatTimestamp(),\n        }\n      ],\n      users: [],\n      createdAt: formatTimestamp(),\n      updatedAt: formatTimestamp(),\n    };\n  }\n\n  async fetch(request: Request): Promise<Response> {\n    const url = new URL(request.url);\n    const pathname = url.pathname;\n\n    // WebSocket升级\n    if (request.headers.get('Upgrade') === 'websocket') {\n      return this.handleWebSocket(request);\n    }\n\n    // REST API路由\n    switch (pathname) {\n      case '/room':\n        return this.handleGetRoom();\n      case '/channels':\n        if (request.method === 'POST') {\n          return this.handleCreateChannel(request);\n        }\n        return this.handleGetChannels();\n      case '/users':\n        if (request.method === 'POST') {\n          return this.handleCreateUser(request);\n        }\n        return this.handleGetUsers();\n      default:\n        if (pathname.startsWith('/channels/')) {\n          const channelId = pathname.split('/')[2];\n          if (request.method === 'PUT') {\n            return this.handleUpdateChannel(channelId, request);\n          } else if (request.method === 'DELETE') {\n            return this.handleDeleteChannel(channelId);\n          }\n        } else if (pathname.startsWith('/users/')) {\n          const userId = pathname.split('/')[2];\n          if (request.method === 'PUT') {\n            return this.handleUpdateUser(userId, request);\n          } else if (request.method === 'DELETE') {\n            return this.handleDeleteUser(userId);\n          }\n        }\n        return new Response('Not Found', { status: 404 });\n    }\n  }\n\n  private async handleWebSocket(request: Request): Promise<Response> {\n    const webSocketPair = new WebSocketPair();\n    const [client, server] = Object.values(webSocketPair);\n\n    const connectionId = generateId();\n    this.connections.set(connectionId, server);\n\n    server.accept();\n\n    server.addEventListener('message', async (event) => {\n      try {\n        const message: WebSocketMessage = JSON.parse(event.data as string);\n        await this.handleWebSocketMessage(connectionId, message);\n      } catch (error) {\n        console.error('WebSocket message error:', error);\n      }\n    });\n\n    server.addEventListener('close', () => {\n      this.connections.delete(connectionId);\n      this.updateLastActivity();\n    });\n\n    server.addEventListener('error', (error) => {\n      console.error('WebSocket error:', error);\n      this.connections.delete(connectionId);\n    });\n\n    this.updateLastActivity();\n    return createWebSocketResponse(client);\n  }\n\n  private async handleWebSocketMessage(connectionId: string, message: WebSocketMessage): Promise<void> {\n    this.updateLastActivity();\n\n    switch (message.type) {\n      case 'JOIN_ROOM':\n        await this.handleJoinRoom(connectionId, message.payload);\n        break;\n      case 'LEAVE_ROOM':\n        await this.handleLeaveRoom(connectionId, message.payload);\n        break;\n      case 'JOIN_CHANNEL':\n        await this.handleJoinChannel(connectionId, message.payload);\n        break;\n      case 'LEAVE_CHANNEL':\n        await this.handleLeaveChannel(connectionId, message.payload);\n        break;\n      case 'SIGNALING':\n        await this.handleSignaling(connectionId, message.payload);\n        break;\n      case 'VOICE_STATE_UPDATE':\n        await this.handleVoiceStateUpdate(connectionId, message.payload);\n        break;\n      default:\n        console.warn('Unknown message type:', message.type);\n    }\n  }\n\n  private async handleGetRoom(): Promise<Response> {\n    return new Response(JSON.stringify({\n      success: true,\n      data: this.room\n    }), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n\n  private async handleCreateChannel(request: Request): Promise<Response> {\n    const body = await request.json();\n    const { name, type = 'voice' } = body;\n\n    if (!name) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Channel name is required'\n      }), { status: 400 });\n    }\n\n    const newChannel: Channel = {\n      id: generateId(),\n      name,\n      type,\n      userCount: 0,\n      users: [],\n      createdAt: formatTimestamp(),\n      updatedAt: formatTimestamp(),\n    };\n\n    this.room.channels.push(newChannel);\n    this.room.updatedAt = formatTimestamp();\n    this.updateLastActivity();\n\n    // 广播频道创建事件\n    this.broadcastEvent({\n      type: 'CHANNEL_CREATE',\n      payload: newChannel\n    });\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: newChannel\n    }), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n\n  private async handleUpdateChannel(channelId: string, request: Request): Promise<Response> {\n    const body = await request.json();\n    const { name } = body;\n\n    const channelIndex = this.room.channels.findIndex(c => c.id === channelId);\n    if (channelIndex === -1) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Channel not found'\n      }), { status: 404 });\n    }\n\n    if (name) {\n      this.room.channels[channelIndex].name = name;\n      this.room.channels[channelIndex].updatedAt = formatTimestamp();\n      this.room.updatedAt = formatTimestamp();\n      this.updateLastActivity();\n\n      // 广播频道更新事件\n      this.broadcastEvent({\n        type: 'CHANNEL_UPDATE',\n        payload: this.room.channels[channelIndex]\n      });\n    }\n\n    return new Response(JSON.stringify({\n      success: true,\n      data: this.room.channels[channelIndex]\n    }), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n\n  private async handleDeleteChannel(channelId: string): Promise<Response> {\n    const channelIndex = this.room.channels.findIndex(c => c.id === channelId);\n    if (channelIndex === -1) {\n      return new Response(JSON.stringify({\n        success: false,\n        error: 'Channel not found'\n      }), { status: 404 });\n    }\n\n    // 移除频道中的所有用户\n    const channel = this.room.channels[channelIndex];\n    for (const user of channel.users) {\n      this.removeUserFromChannel(user.id, channelId);\n    }\n\n    this.room.channels.splice(channelIndex, 1);\n    this.room.updatedAt = formatTimestamp();\n    this.updateLastActivity();\n\n    // 广播频道删除事件\n    this.broadcastEvent({\n      type: 'CHANNEL_DELETE',\n      payload: { channelId }\n    });\n\n    return new Response(JSON.stringify({\n      success: true,\n      message: 'Channel deleted successfully'\n    }), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n\n  private broadcastEvent(event: RoomEvent): void {\n    const message = {\n      type: 'ROOM_EVENT',\n      payload: event,\n      timestamp: formatTimestamp()\n    };\n\n    broadcastToConnections(this.connections, message);\n  }\n\n  private updateLastActivity(): void {\n    this.lastActivity = formatTimestamp();\n  }\n\n  private removeUserFromChannel(userId: string, channelId: string): void {\n    const channel = this.room.channels.find(c => c.id === channelId);\n    if (channel) {\n      channel.users = channel.users.filter(u => u.id !== userId);\n      channel.userCount = channel.users.length;\n      channel.updatedAt = formatTimestamp();\n    }\n  }\n\n  // 其他处理方法的占位符\n  private async handleGetChannels(): Promise<Response> {\n    return new Response(JSON.stringify({\n      success: true,\n      data: this.room.channels\n    }), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n\n  private async handleCreateUser(request: Request): Promise<Response> {\n    // 实现用户创建逻辑\n    return new Response('Not implemented', { status: 501 });\n  }\n\n  private async handleGetUsers(): Promise<Response> {\n    return new Response(JSON.stringify({\n      success: true,\n      data: this.room.users\n    }), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n\n  private async handleUpdateUser(userId: string, request: Request): Promise<Response> {\n    // 实现用户更新逻辑\n    return new Response('Not implemented', { status: 501 });\n  }\n\n  private async handleDeleteUser(userId: string): Promise<Response> {\n    // 实现用户删除逻辑\n    return new Response('Not implemented', { status: 501 });\n  }\n\n  private async handleJoinRoom(connectionId: string, payload: any): Promise<void> {\n    // 实现加入房间逻辑\n  }\n\n  private async handleLeaveRoom(connectionId: string, payload: any): Promise<void> {\n    // 实现离开房间逻辑\n  }\n\n  private async handleJoinChannel(connectionId: string, payload: any): Promise<void> {\n    // 实现加入频道逻辑\n  }\n\n  private async handleLeaveChannel(connectionId: string, payload: any): Promise<void> {\n    // 实现离开频道逻辑\n  }\n\n  private async handleSignaling(connectionId: string, payload: SignalingMessage): Promise<void> {\n    // 实现WebRTC信令逻辑\n    this.broadcastEvent({\n      type: 'SIGNALING',\n      payload\n    });\n  }\n\n  private async handleVoiceStateUpdate(connectionId: string, payload: any): Promise<void> {\n    // 实现语音状态更新逻辑\n  }\n}\n", "\r\nimport { User } from '../types';\r\nimport { formatTimestamp } from '../utils';\r\n\r\nexport class UserState implements DurableObject {\r\n  private user: User | null = null;\r\n  private webSocket: WebSocket | null = null;\r\n\r\n  constructor(private state: DurableObjectState, private env: any) {}\r\n\r\n  async fetch(request: Request): Promise<Response> {\r\n    if (request.headers.get(\"Upgrade\") !== \"websocket\") {\r\n      return new Response(\"Expected a WebSocket connection\", { status: 400 });\r\n    }\r\n\r\n    const pair = new WebSocketPair();\r\n    const [client, server] = Object.values(pair);\r\n\r\n    server.accept();\r\n    this.webSocket = server;\r\n\r\n    server.addEventListener(\"message\", (event: any) => {\r\n      console.log(\"UserState received message:\", event.data);\r\n    });\r\n\r\n    server.addEventListener(\"close\", () => {\r\n      this.webSocket = null;\r\n    });\r\n\r\n    return new Response(null, { status: 101, webSocket: client });\r\n  }\r\n\r\n  async setUser(user: User) {\r\n    this.user = user;\r\n    await this.state.storage.put(\"user\", this.user);\r\n  }\r\n} ", "import { Env, CallsSession, CallsTrack, SessionDescription } from '../types';\nimport { createSuccessResponse, createErrorResponse, createJsonResponse } from '../utils';\n\nexport class CallsHandler {\n  private baseUrl = 'https://rtc.live.cloudflare.com/v1';\n\n  constructor(private env: Env) {}\n\n  // 创建新的WebRTC会话\n  async createSession(correlationId?: string): Promise<Response> {\n    try {\n      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/new`;\n      const params = new URLSearchParams();\n      \n      if (correlationId) {\n        params.append('correlationId', correlationId);\n      }\n\n      const response = await fetch(`${url}?${params.toString()}`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Calls API error:', errorText);\n        return createJsonResponse(\n          createErrorResponse('Failed to create session', errorText),\n          response.status\n        );\n      }\n\n      const sessionData = await response.json();\n      return createJsonResponse(createSuccessResponse(sessionData));\n    } catch (error) {\n      console.error('Create session error:', error);\n      return createJsonResponse(\n        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  }\n\n  // 添加音轨到会话\n  async addTracks(sessionId: string, tracks: any[], sessionDescription?: SessionDescription): Promise<Response> {\n    try {\n      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/new`;\n      \n      const requestBody: any = {\n        tracks\n      };\n\n      if (sessionDescription) {\n        requestBody.sessionDescription = sessionDescription;\n      }\n\n      const response = await fetch(url, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Add tracks error:', errorText);\n        return createJsonResponse(\n          createErrorResponse('Failed to add tracks', errorText),\n          response.status\n        );\n      }\n\n      const tracksData = await response.json();\n      return createJsonResponse(createSuccessResponse(tracksData));\n    } catch (error) {\n      console.error('Add tracks error:', error);\n      return createJsonResponse(\n        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  }\n\n  // 重新协商WebRTC会话\n  async renegotiate(sessionId: string, sessionDescription: SessionDescription): Promise<Response> {\n    try {\n      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/renegotiate`;\n      \n      const response = await fetch(url, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ sessionDescription }),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Renegotiate error:', errorText);\n        return createJsonResponse(\n          createErrorResponse('Failed to renegotiate', errorText),\n          response.status\n        );\n      }\n\n      const renegotiateData = await response.json();\n      return createJsonResponse(createSuccessResponse(renegotiateData));\n    } catch (error) {\n      console.error('Renegotiate error:', error);\n      return createJsonResponse(\n        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  }\n\n  // 关闭音轨\n  async closeTracks(sessionId: string, tracks: any[], sessionDescription?: SessionDescription, force?: boolean): Promise<Response> {\n    try {\n      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/close`;\n      \n      const requestBody: any = {\n        tracks\n      };\n\n      if (sessionDescription) {\n        requestBody.sessionDescription = sessionDescription;\n      }\n\n      if (force !== undefined) {\n        requestBody.force = force;\n      }\n\n      const response = await fetch(url, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Close tracks error:', errorText);\n        return createJsonResponse(\n          createErrorResponse('Failed to close tracks', errorText),\n          response.status\n        );\n      }\n\n      const closeData = await response.json();\n      return createJsonResponse(createSuccessResponse(closeData));\n    } catch (error) {\n      console.error('Close tracks error:', error);\n      return createJsonResponse(\n        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  }\n\n  // 更新音轨\n  async updateTracks(sessionId: string, tracks: any[]): Promise<Response> {\n    try {\n      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}/tracks/update`;\n      \n      const response = await fetch(url, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ tracks }),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Update tracks error:', errorText);\n        return createJsonResponse(\n          createErrorResponse('Failed to update tracks', errorText),\n          response.status\n        );\n      }\n\n      const updateData = await response.json();\n      return createJsonResponse(createSuccessResponse(updateData));\n    } catch (error) {\n      console.error('Update tracks error:', error);\n      return createJsonResponse(\n        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  }\n\n  // 获取会话状态\n  async getSessionState(sessionId: string): Promise<Response> {\n    try {\n      const url = `${this.baseUrl}/apps/${this.env.CLOUDFLARE_CALLS_APP_ID}/sessions/${sessionId}`;\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${this.env.CLOUDFLARE_CALLS_SECRET}`,\n        },\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Get session state error:', errorText);\n        return createJsonResponse(\n          createErrorResponse('Failed to get session state', errorText),\n          response.status\n        );\n      }\n\n      const sessionData = await response.json();\n      return createJsonResponse(createSuccessResponse(sessionData));\n    } catch (error) {\n      console.error('Get session state error:', error);\n      return createJsonResponse(\n        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  }\n\n  // 处理WebRTC信令\n  async handleSignaling(request: Request): Promise<Response> {\n    const url = new URL(request.url);\n    const pathname = url.pathname;\n    const sessionId = url.searchParams.get('sessionId');\n\n    if (!sessionId) {\n      return createJsonResponse(\n        createErrorResponse('Session ID is required'),\n        400\n      );\n    }\n\n    try {\n      const body = await request.json();\n\n      switch (pathname) {\n        case '/calls/sessions/new':\n          return this.createSession(body.correlationId);\n        \n        case '/calls/tracks/add':\n          return this.addTracks(sessionId, body.tracks, body.sessionDescription);\n        \n        case '/calls/renegotiate':\n          return this.renegotiate(sessionId, body.sessionDescription);\n        \n        case '/calls/tracks/close':\n          return this.closeTracks(sessionId, body.tracks, body.sessionDescription, body.force);\n        \n        case '/calls/tracks/update':\n          return this.updateTracks(sessionId, body.tracks);\n        \n        case '/calls/session/state':\n          return this.getSessionState(sessionId);\n        \n        default:\n          return createJsonResponse(\n            createErrorResponse('Unknown endpoint'),\n            404\n          );\n      }\n    } catch (error) {\n      console.error('Signaling handler error:', error);\n      return createJsonResponse(\n        createErrorResponse('Failed to process signaling request', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  }\n}\n", "import { Env } from './types';\nimport { RoomState } from './handlers/RoomState';\nimport { UserState } from './handlers/UserState';\nimport { CallsHandler } from './handlers/CallsHandler';\nimport { \n  handleCorsOptions, \n  createJsonResponse, \n  createErrorResponse, \n  withErrorHandling,\n  RateLimiter \n} from './utils';\n\n// 导出Durable Object类\nexport { RoomState, UserState };\n\n// 全局限流器\nconst rateLimiter = new RateLimiter(100, 60000); // 每分钟100个请求\n\nexport default {\n  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {\n    // 处理CORS预检请求\n    if (request.method === 'OPTIONS') {\n      return handleCorsOptions();\n    }\n\n    // 获取客户端IP进行限流\n    const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';\n    if (!rateLimiter.isAllowed(clientIP)) {\n      return createJsonResponse(\n        createErrorResponse('Rate limit exceeded'),\n        429\n      );\n    }\n\n    const url = new URL(request.url);\n    const pathname = url.pathname;\n\n    try {\n      // 路由处理\n      if (pathname.startsWith('/api/room') || pathname.startsWith('/ws')) {\n        return await handleRoomRequests(request, env);\n      } else if (pathname.startsWith('/api/calls')) {\n        return await handleCallsRequests(request, env);\n      } else if (pathname === '/api/health') {\n        return createJsonResponse({\n          success: true,\n          data: {\n            status: 'healthy',\n            timestamp: new Date().toISOString(),\n            version: '1.0.0'\n          }\n        });\n      } else {\n        return createJsonResponse(\n          createErrorResponse('Not Found', 'The requested endpoint was not found'),\n          404\n        );\n      }\n    } catch (error) {\n      console.error('Request handling error:', error);\n      return createJsonResponse(\n        createErrorResponse('Internal Server Error', error instanceof Error ? error.message : 'Unknown error'),\n        500\n      );\n    }\n  },\n};\n\n// 处理房间相关请求\nasync function handleRoomRequests(request: Request, env: Env): Promise<Response> {\n  // 获取房间状态的Durable Object\n  const roomId = 'default-room'; // 目前只有一个房间\n  const durableObjectId = env.ROOM_STATE.idFromName(roomId);\n  const roomState = env.ROOM_STATE.get(durableObjectId);\n\n  // 转发请求到Durable Object\n  const url = new URL(request.url);\n  \n  // 移除API前缀\n  const newPathname = url.pathname.replace('/api/room', '');\n  url.pathname = newPathname || '/room';\n\n  // WebSocket升级请求\n  if (url.pathname === '/ws' || request.headers.get('Upgrade') === 'websocket') {\n    url.pathname = '/ws';\n    const newRequest = new Request(url.toString(), request);\n    return await roomState.fetch(newRequest);\n  }\n\n  // 普通HTTP请求\n  const newRequest = new Request(url.toString(), {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n  });\n\n  return await roomState.fetch(newRequest);\n}\n\n// 处理Cloudflare Calls相关请求\nasync function handleCallsRequests(request: Request, env: Env): Promise<Response> {\n  const callsHandler = new CallsHandler(env);\n  \n  const url = new URL(request.url);\n  const pathname = url.pathname.replace('/api/calls', '');\n  \n  // 创建新的URL用于处理\n  const newUrl = new URL(request.url);\n  newUrl.pathname = pathname;\n  \n  const newRequest = new Request(newUrl.toString(), {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n  });\n\n  return await callsHandler.handleSignaling(newRequest);\n}\n\n// 定期清理任务（可选）\nexport async function scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {\n  // 清理限流器\n  rateLimiter.cleanup();\n  \n  // 这里可以添加其他定期清理任务\n  console.log('Scheduled cleanup completed');\n}\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\opz\\\\backend\\\\src\\\\index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\opz\\\\backend\\\\src\\\\index.ts\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\opz\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-uSIFVA\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\opz\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-uSIFVA\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\opz\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-uSIFVA\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS,CAAC;AAAA;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC1BM,SAAS,aAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAFgB;AAwBT,SAAS,sBAAyB,MAAS,SAAkC;AAClF,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;AANgB;AAST,SAAS,oBAAoB,OAAe,SAA+B;AAChF,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;AANgB;AAST,SAAS,mBAAsB,MAAsB,SAAiB,KAAe;AAC1F,SAAO,IAAI,SAAS,KAAK,UAAU,IAAI,GAAG;AAAA,IACxC;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,+BAA+B;AAAA,MAC/B,gCAAgC;AAAA,MAChC,gCAAgC;AAAA,IAClC;AAAA,EACF,CAAC;AACH;AAVgB;AAaT,SAAS,oBAA8B;AAC5C,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,+BAA+B;AAAA,MAC/B,gCAAgC;AAAA,MAChC,gCAAgC;AAAA,MAChC,0BAA0B;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AAVgB;AAqCT,SAAS,gBAAgB,OAAa,oBAAI,KAAK,GAAW;AAC/D,SAAO,KAAK,YAAY;AAC1B;AAFgB;AAaT,SAAS,uBAAuB,aAAqC,SAAc,WAA0B;AAClH,QAAM,aAAa,KAAK,UAAU,OAAO;AAEzC,aAAW,CAAC,cAAc,EAAE,KAAK,YAAY,QAAQ,GAAG;AACtD,QAAI,aAAa,iBAAiB,WAAW;AAC3C;AAAA,IACF;AAEA,QAAI;AACF,UAAI,GAAG,eAAe,UAAU,kBAAkB;AAChD,WAAG,KAAK,UAAU;AAAA,MACpB,OAAO;AAEL,oBAAY,OAAO,YAAY;AAAA,MACjC;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,wCAAwC,YAAY,KAAK,KAAK;AAC5E,kBAAY,OAAO,YAAY;AAAA,IACjC;AAAA,EACF;AACF;AApBgB;AAuBT,SAAS,wBAAwB,WAAgC;AACtE,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,QAAQ;AAAA,IACR;AAAA,EACF,CAAC;AACH;AALgB;AAyBT,IAAM,cAAN,MAAkB;AAAA,EAGvB,YACU,cAAsB,KACtB,WAAmB,KAC3B;AAFQ;AACA;AAAA,EACP;AAAA,EAlKL,OA4JyB;AAAA;AAAA;AAAA,EACf,WAAkC,oBAAI,IAAI;AAAA,EAOlD,UAAU,YAA6B;AACrC,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,cAAc,MAAM,KAAK;AAG/B,QAAI,WAAW,KAAK,SAAS,IAAI,UAAU,KAAK,CAAC;AAGjD,eAAW,SAAS,OAAO,eAAa,YAAY,WAAW;AAG/D,QAAI,SAAS,UAAU,KAAK,aAAa;AACvC,aAAO;AAAA,IACT;AAGA,aAAS,KAAK,GAAG;AACjB,SAAK,SAAS,IAAI,YAAY,QAAQ;AAEtC,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,UAAgB;AACd,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,cAAc,MAAM,KAAK;AAE/B,eAAW,CAAC,YAAY,QAAQ,KAAK,KAAK,SAAS,QAAQ,GAAG;AAC5D,YAAM,gBAAgB,SAAS,OAAO,eAAa,YAAY,WAAW;AAC1E,UAAI,cAAc,WAAW,GAAG;AAC9B,aAAK,SAAS,OAAO,UAAU;AAAA,MACjC,OAAO;AACL,aAAK,SAAS,IAAI,YAAY,aAAa;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AACF;;;ACxLO,IAAM,YAAN,MAAyC;AAAA,EAK9C,YAAoB,OAAmC,KAAU;AAA7C;AAAmC;AAErD,SAAK,OAAO;AAAA,MACV,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,QACR;AAAA,UACE,IAAI,WAAW;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO,CAAC;AAAA,UACR,WAAW,gBAAgB;AAAA,UAC3B,WAAW,gBAAgB;AAAA,QAC7B;AAAA,QACA;AAAA,UACE,IAAI,WAAW;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO,CAAC;AAAA,UACR,WAAW,gBAAgB;AAAA,UAC3B,WAAW,gBAAgB;AAAA,QAC7B;AAAA,MACF;AAAA,MACA,OAAO,CAAC;AAAA,MACR,WAAW,gBAAgB;AAAA,MAC3B,WAAW,gBAAgB;AAAA,IAC7B;AAAA,EACF;AAAA,EAlDF,OAgBgD;AAAA;AAAA;AAAA,EACtC;AAAA,EACA,cAAsC,oBAAI,IAAI;AAAA,EAC9C,eAAuB,gBAAgB;AAAA,EAiC/C,MAAM,MAAM,SAAqC;AAC/C,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,WAAW,IAAI;AAGrB,QAAI,QAAQ,QAAQ,IAAI,SAAS,MAAM,aAAa;AAClD,aAAO,KAAK,gBAAgB,OAAO;AAAA,IACrC;AAGA,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,eAAO,KAAK,cAAc;AAAA,MAC5B,KAAK;AACH,YAAI,QAAQ,WAAW,QAAQ;AAC7B,iBAAO,KAAK,oBAAoB,OAAO;AAAA,QACzC;AACA,eAAO,KAAK,kBAAkB;AAAA,MAChC,KAAK;AACH,YAAI,QAAQ,WAAW,QAAQ;AAC7B,iBAAO,KAAK,iBAAiB,OAAO;AAAA,QACtC;AACA,eAAO,KAAK,eAAe;AAAA,MAC7B;AACE,YAAI,SAAS,WAAW,YAAY,GAAG;AACrC,gBAAM,YAAY,SAAS,MAAM,GAAG,EAAE,CAAC;AACvC,cAAI,QAAQ,WAAW,OAAO;AAC5B,mBAAO,KAAK,oBAAoB,WAAW,OAAO;AAAA,UACpD,WAAW,QAAQ,WAAW,UAAU;AACtC,mBAAO,KAAK,oBAAoB,SAAS;AAAA,UAC3C;AAAA,QACF,WAAW,SAAS,WAAW,SAAS,GAAG;AACzC,gBAAM,SAAS,SAAS,MAAM,GAAG,EAAE,CAAC;AACpC,cAAI,QAAQ,WAAW,OAAO;AAC5B,mBAAO,KAAK,iBAAiB,QAAQ,OAAO;AAAA,UAC9C,WAAW,QAAQ,WAAW,UAAU;AACtC,mBAAO,KAAK,iBAAiB,MAAM;AAAA,UACrC;AAAA,QACF;AACA,eAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,IACpD;AAAA,EACF;AAAA,EAEA,MAAc,gBAAgB,SAAqC;AACjE,UAAM,gBAAgB,IAAI,cAAc;AACxC,UAAM,CAAC,QAAQ,MAAM,IAAI,OAAO,OAAO,aAAa;AAEpD,UAAM,eAAe,WAAW;AAChC,SAAK,YAAY,IAAI,cAAc,MAAM;AAEzC,WAAO,OAAO;AAEd,WAAO,iBAAiB,WAAW,OAAO,UAAU;AAClD,UAAI;AACF,cAAM,UAA4B,KAAK,MAAM,MAAM,IAAc;AACjE,cAAM,KAAK,uBAAuB,cAAc,OAAO;AAAA,MACzD,SAAS,OAAO;AACd,gBAAQ,MAAM,4BAA4B,KAAK;AAAA,MACjD;AAAA,IACF,CAAC;AAED,WAAO,iBAAiB,SAAS,MAAM;AACrC,WAAK,YAAY,OAAO,YAAY;AACpC,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AAED,WAAO,iBAAiB,SAAS,CAAC,UAAU;AAC1C,cAAQ,MAAM,oBAAoB,KAAK;AACvC,WAAK,YAAY,OAAO,YAAY;AAAA,IACtC,CAAC;AAED,SAAK,mBAAmB;AACxB,WAAO,wBAAwB,MAAM;AAAA,EACvC;AAAA,EAEA,MAAc,uBAAuB,cAAsB,SAA0C;AACnG,SAAK,mBAAmB;AAExB,YAAQ,QAAQ,MAAM;AAAA,MACpB,KAAK;AACH,cAAM,KAAK,eAAe,cAAc,QAAQ,OAAO;AACvD;AAAA,MACF,KAAK;AACH,cAAM,KAAK,gBAAgB,cAAc,QAAQ,OAAO;AACxD;AAAA,MACF,KAAK;AACH,cAAM,KAAK,kBAAkB,cAAc,QAAQ,OAAO;AAC1D;AAAA,MACF,KAAK;AACH,cAAM,KAAK,mBAAmB,cAAc,QAAQ,OAAO;AAC3D;AAAA,MACF,KAAK;AACH,cAAM,KAAK,gBAAgB,cAAc,QAAQ,OAAO;AACxD;AAAA,MACF,KAAK;AACH,cAAM,KAAK,uBAAuB,cAAc,QAAQ,OAAO;AAC/D;AAAA,MACF;AACE,gBAAQ,KAAK,yBAAyB,QAAQ,IAAI;AAAA,IACtD;AAAA,EACF;AAAA,EAEA,MAAc,gBAAmC;AAC/C,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM,KAAK;AAAA,IACb,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EAEA,MAAc,oBAAoB,SAAqC;AACrE,UAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,UAAM,EAAE,MAAM,OAAO,QAAQ,IAAI;AAEjC,QAAI,CAAC,MAAM;AACT,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG,EAAE,QAAQ,IAAI,CAAC;AAAA,IACrB;AAEA,UAAM,aAAsB;AAAA,MAC1B,IAAI,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX,OAAO,CAAC;AAAA,MACR,WAAW,gBAAgB;AAAA,MAC3B,WAAW,gBAAgB;AAAA,IAC7B;AAEA,SAAK,KAAK,SAAS,KAAK,UAAU;AAClC,SAAK,KAAK,YAAY,gBAAgB;AACtC,SAAK,mBAAmB;AAGxB,SAAK,eAAe;AAAA,MAClB,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAED,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EAEA,MAAc,oBAAoB,WAAmB,SAAqC;AACxF,UAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,UAAM,EAAE,KAAK,IAAI;AAEjB,UAAM,eAAe,KAAK,KAAK,SAAS,UAAU,OAAK,EAAE,OAAO,SAAS;AACzE,QAAI,iBAAiB,IAAI;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG,EAAE,QAAQ,IAAI,CAAC;AAAA,IACrB;AAEA,QAAI,MAAM;AACR,WAAK,KAAK,SAAS,YAAY,EAAE,OAAO;AACxC,WAAK,KAAK,SAAS,YAAY,EAAE,YAAY,gBAAgB;AAC7D,WAAK,KAAK,YAAY,gBAAgB;AACtC,WAAK,mBAAmB;AAGxB,WAAK,eAAe;AAAA,QAClB,MAAM;AAAA,QACN,SAAS,KAAK,KAAK,SAAS,YAAY;AAAA,MAC1C,CAAC;AAAA,IACH;AAEA,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM,KAAK,KAAK,SAAS,YAAY;AAAA,IACvC,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EAEA,MAAc,oBAAoB,WAAsC;AACtE,UAAM,eAAe,KAAK,KAAK,SAAS,UAAU,OAAK,EAAE,OAAO,SAAS;AACzE,QAAI,iBAAiB,IAAI;AACvB,aAAO,IAAI,SAAS,KAAK,UAAU;AAAA,QACjC,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG,EAAE,QAAQ,IAAI,CAAC;AAAA,IACrB;AAGA,UAAM,UAAU,KAAK,KAAK,SAAS,YAAY;AAC/C,eAAW,QAAQ,QAAQ,OAAO;AAChC,WAAK,sBAAsB,KAAK,IAAI,SAAS;AAAA,IAC/C;AAEA,SAAK,KAAK,SAAS,OAAO,cAAc,CAAC;AACzC,SAAK,KAAK,YAAY,gBAAgB;AACtC,SAAK,mBAAmB;AAGxB,SAAK,eAAe;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,EAAE,UAAU;AAAA,IACvB,CAAC;AAED,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EAEQ,eAAe,OAAwB;AAC7C,UAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW,gBAAgB;AAAA,IAC7B;AAEA,2BAAuB,KAAK,aAAa,OAAO;AAAA,EAClD;AAAA,EAEQ,qBAA2B;AACjC,SAAK,eAAe,gBAAgB;AAAA,EACtC;AAAA,EAEQ,sBAAsB,QAAgB,WAAyB;AACrE,UAAM,UAAU,KAAK,KAAK,SAAS,KAAK,OAAK,EAAE,OAAO,SAAS;AAC/D,QAAI,SAAS;AACX,cAAQ,QAAQ,QAAQ,MAAM,OAAO,OAAK,EAAE,OAAO,MAAM;AACzD,cAAQ,YAAY,QAAQ,MAAM;AAClC,cAAQ,YAAY,gBAAgB;AAAA,IACtC;AAAA,EACF;AAAA;AAAA,EAGA,MAAc,oBAAuC;AACnD,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM,KAAK,KAAK;AAAA,IAClB,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EAEA,MAAc,iBAAiB,SAAqC;AAElE,WAAO,IAAI,SAAS,mBAAmB,EAAE,QAAQ,IAAI,CAAC;AAAA,EACxD;AAAA,EAEA,MAAc,iBAAoC;AAChD,WAAO,IAAI,SAAS,KAAK,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,MAAM,KAAK,KAAK;AAAA,IAClB,CAAC,GAAG;AAAA,MACF,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EAEA,MAAc,iBAAiB,QAAgB,SAAqC;AAElF,WAAO,IAAI,SAAS,mBAAmB,EAAE,QAAQ,IAAI,CAAC;AAAA,EACxD;AAAA,EAEA,MAAc,iBAAiB,QAAmC;AAEhE,WAAO,IAAI,SAAS,mBAAmB,EAAE,QAAQ,IAAI,CAAC;AAAA,EACxD;AAAA,EAEA,MAAc,eAAe,cAAsB,SAA6B;AAAA,EAEhF;AAAA,EAEA,MAAc,gBAAgB,cAAsB,SAA6B;AAAA,EAEjF;AAAA,EAEA,MAAc,kBAAkB,cAAsB,SAA6B;AAAA,EAEnF;AAAA,EAEA,MAAc,mBAAmB,cAAsB,SAA6B;AAAA,EAEpF;AAAA,EAEA,MAAc,gBAAgB,cAAsB,SAA0C;AAE5F,SAAK,eAAe;AAAA,MAClB,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAc,uBAAuB,cAAsB,SAA6B;AAAA,EAExF;AACF;;;AC5VO,IAAM,YAAN,MAAyC;AAAA,EAI9C,YAAoB,OAAmC,KAAU;AAA7C;AAAmC;AAAA,EAAW;AAAA,EARpE,OAIgD;AAAA;AAAA;AAAA,EACtC,OAAoB;AAAA,EACpB,YAA8B;AAAA,EAItC,MAAM,MAAM,SAAqC;AAC/C,QAAI,QAAQ,QAAQ,IAAI,SAAS,MAAM,aAAa;AAClD,aAAO,IAAI,SAAS,mCAAmC,EAAE,QAAQ,IAAI,CAAC;AAAA,IACxE;AAEA,UAAM,OAAO,IAAI,cAAc;AAC/B,UAAM,CAAC,QAAQ,MAAM,IAAI,OAAO,OAAO,IAAI;AAE3C,WAAO,OAAO;AACd,SAAK,YAAY;AAEjB,WAAO,iBAAiB,WAAW,CAAC,UAAe;AACjD,cAAQ,IAAI,+BAA+B,MAAM,IAAI;AAAA,IACvD,CAAC;AAED,WAAO,iBAAiB,SAAS,MAAM;AACrC,WAAK,YAAY;AAAA,IACnB,CAAC;AAED,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,KAAK,WAAW,OAAO,CAAC;AAAA,EAC9D;AAAA,EAEA,MAAM,QAAQ,MAAY;AACxB,SAAK,OAAO;AACZ,UAAM,KAAK,MAAM,QAAQ,IAAI,QAAQ,KAAK,IAAI;AAAA,EAChD;AACF;;;ACjCO,IAAM,eAAN,MAAmB;AAAA,EAGxB,YAAoB,KAAU;AAAV;AAAA,EAAW;AAAA,EANjC,OAG0B;AAAA;AAAA;AAAA,EAChB,UAAU;AAAA;AAAA,EAKlB,MAAM,cAAc,eAA2C;AAC7D,QAAI;AACF,YAAM,MAAM,GAAG,KAAK,OAAO,SAAS,KAAK,IAAI,uBAAuB;AACpE,YAAM,SAAS,IAAI,gBAAgB;AAEnC,UAAI,eAAe;AACjB,eAAO,OAAO,iBAAiB,aAAa;AAAA,MAC9C;AAEA,YAAM,WAAW,MAAM,MAAM,GAAG,GAAG,IAAI,OAAO,SAAS,CAAC,IAAI;AAAA,QAC1D,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,iBAAiB,UAAU,KAAK,IAAI,uBAAuB;AAAA,UAC3D,gBAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAQ,MAAM,oBAAoB,SAAS;AAC3C,eAAO;AAAA,UACL,oBAAoB,4BAA4B,SAAS;AAAA,UACzD,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,cAAc,MAAM,SAAS,KAAK;AACxC,aAAO,mBAAmB,sBAAsB,WAAW,CAAC;AAAA,IAC9D,SAAS,OAAO;AACd,cAAQ,MAAM,yBAAyB,KAAK;AAC5C,aAAO;AAAA,QACL,oBAAoB,yBAAyB,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,UAAU,WAAmB,QAAe,oBAA4D;AAC5G,QAAI;AACF,YAAM,MAAM,GAAG,KAAK,OAAO,SAAS,KAAK,IAAI,uBAAuB,aAAa,SAAS;AAE1F,YAAM,cAAmB;AAAA,QACvB;AAAA,MACF;AAEA,UAAI,oBAAoB;AACtB,oBAAY,qBAAqB;AAAA,MACnC;AAEA,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QAChC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,iBAAiB,UAAU,KAAK,IAAI,uBAAuB;AAAA,UAC3D,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,WAAW;AAAA,MAClC,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAQ,MAAM,qBAAqB,SAAS;AAC5C,eAAO;AAAA,UACL,oBAAoB,wBAAwB,SAAS;AAAA,UACrD,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,aAAa,MAAM,SAAS,KAAK;AACvC,aAAO,mBAAmB,sBAAsB,UAAU,CAAC;AAAA,IAC7D,SAAS,OAAO;AACd,cAAQ,MAAM,qBAAqB,KAAK;AACxC,aAAO;AAAA,QACL,oBAAoB,yBAAyB,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,YAAY,WAAmB,oBAA2D;AAC9F,QAAI;AACF,YAAM,MAAM,GAAG,KAAK,OAAO,SAAS,KAAK,IAAI,uBAAuB,aAAa,SAAS;AAE1F,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QAChC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,iBAAiB,UAAU,KAAK,IAAI,uBAAuB;AAAA,UAC3D,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,mBAAmB,CAAC;AAAA,MAC7C,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAQ,MAAM,sBAAsB,SAAS;AAC7C,eAAO;AAAA,UACL,oBAAoB,yBAAyB,SAAS;AAAA,UACtD,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,kBAAkB,MAAM,SAAS,KAAK;AAC5C,aAAO,mBAAmB,sBAAsB,eAAe,CAAC;AAAA,IAClE,SAAS,OAAO;AACd,cAAQ,MAAM,sBAAsB,KAAK;AACzC,aAAO;AAAA,QACL,oBAAoB,yBAAyB,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,YAAY,WAAmB,QAAe,oBAAyC,OAAoC;AAC/H,QAAI;AACF,YAAM,MAAM,GAAG,KAAK,OAAO,SAAS,KAAK,IAAI,uBAAuB,aAAa,SAAS;AAE1F,YAAM,cAAmB;AAAA,QACvB;AAAA,MACF;AAEA,UAAI,oBAAoB;AACtB,oBAAY,qBAAqB;AAAA,MACnC;AAEA,UAAI,UAAU,QAAW;AACvB,oBAAY,QAAQ;AAAA,MACtB;AAEA,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QAChC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,iBAAiB,UAAU,KAAK,IAAI,uBAAuB;AAAA,UAC3D,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,WAAW;AAAA,MAClC,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAQ,MAAM,uBAAuB,SAAS;AAC9C,eAAO;AAAA,UACL,oBAAoB,0BAA0B,SAAS;AAAA,UACvD,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,YAAY,MAAM,SAAS,KAAK;AACtC,aAAO,mBAAmB,sBAAsB,SAAS,CAAC;AAAA,IAC5D,SAAS,OAAO;AACd,cAAQ,MAAM,uBAAuB,KAAK;AAC1C,aAAO;AAAA,QACL,oBAAoB,yBAAyB,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,aAAa,WAAmB,QAAkC;AACtE,QAAI;AACF,YAAM,MAAM,GAAG,KAAK,OAAO,SAAS,KAAK,IAAI,uBAAuB,aAAa,SAAS;AAE1F,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QAChC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,iBAAiB,UAAU,KAAK,IAAI,uBAAuB;AAAA,UAC3D,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,OAAO,CAAC;AAAA,MACjC,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAQ,MAAM,wBAAwB,SAAS;AAC/C,eAAO;AAAA,UACL,oBAAoB,2BAA2B,SAAS;AAAA,UACxD,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,aAAa,MAAM,SAAS,KAAK;AACvC,aAAO,mBAAmB,sBAAsB,UAAU,CAAC;AAAA,IAC7D,SAAS,OAAO;AACd,cAAQ,MAAM,wBAAwB,KAAK;AAC3C,aAAO;AAAA,QACL,oBAAoB,yBAAyB,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,gBAAgB,WAAsC;AAC1D,QAAI;AACF,YAAM,MAAM,GAAG,KAAK,OAAO,SAAS,KAAK,IAAI,uBAAuB,aAAa,SAAS;AAE1F,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QAChC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,iBAAiB,UAAU,KAAK,IAAI,uBAAuB;AAAA,QAC7D;AAAA,MACF,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAQ,MAAM,4BAA4B,SAAS;AACnD,eAAO;AAAA,UACL,oBAAoB,+BAA+B,SAAS;AAAA,UAC5D,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,cAAc,MAAM,SAAS,KAAK;AACxC,aAAO,mBAAmB,sBAAsB,WAAW,CAAC;AAAA,IAC9D,SAAS,OAAO;AACd,cAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAO;AAAA,QACL,oBAAoB,yBAAyB,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,gBAAgB,SAAqC;AACzD,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,WAAW,IAAI;AACrB,UAAM,YAAY,IAAI,aAAa,IAAI,WAAW;AAElD,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,QACL,oBAAoB,wBAAwB;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AAEA,QAAI;AACF,YAAM,OAAO,MAAM,QAAQ,KAAK;AAEhC,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,iBAAO,KAAK,cAAc,KAAK,aAAa;AAAA,QAE9C,KAAK;AACH,iBAAO,KAAK,UAAU,WAAW,KAAK,QAAQ,KAAK,kBAAkB;AAAA,QAEvE,KAAK;AACH,iBAAO,KAAK,YAAY,WAAW,KAAK,kBAAkB;AAAA,QAE5D,KAAK;AACH,iBAAO,KAAK,YAAY,WAAW,KAAK,QAAQ,KAAK,oBAAoB,KAAK,KAAK;AAAA,QAErF,KAAK;AACH,iBAAO,KAAK,aAAa,WAAW,KAAK,MAAM;AAAA,QAEjD,KAAK;AACH,iBAAO,KAAK,gBAAgB,SAAS;AAAA,QAEvC;AACE,iBAAO;AAAA,YACL,oBAAoB,kBAAkB;AAAA,YACtC;AAAA,UACF;AAAA,MACJ;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAO;AAAA,QACL,oBAAoB,uCAAuC,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACnH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC3QA,IAAM,cAAc,IAAI,YAAY,KAAK,GAAK;AAE9C,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAkB,KAAU,KAA0C;AAEhF,QAAI,QAAQ,WAAW,WAAW;AAChC,aAAO,kBAAkB;AAAA,IAC3B;AAGA,UAAM,WAAW,QAAQ,QAAQ,IAAI,kBAAkB,KAAK;AAC5D,QAAI,CAAC,YAAY,UAAU,QAAQ,GAAG;AACpC,aAAO;AAAA,QACL,oBAAoB,qBAAqB;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAEA,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,UAAM,WAAW,IAAI;AAErB,QAAI;AAEF,UAAI,SAAS,WAAW,WAAW,KAAK,SAAS,WAAW,KAAK,GAAG;AAClE,eAAO,MAAM,mBAAmB,SAAS,GAAG;AAAA,MAC9C,WAAW,SAAS,WAAW,YAAY,GAAG;AAC5C,eAAO,MAAM,oBAAoB,SAAS,GAAG;AAAA,MAC/C,WAAW,aAAa,eAAe;AACrC,eAAO,mBAAmB;AAAA,UACxB,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,QAAQ;AAAA,YACR,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,YAClC,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,eAAO;AAAA,UACL,oBAAoB,aAAa,sCAAsC;AAAA,UACvE;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,2BAA2B,KAAK;AAC9C,aAAO;AAAA,QACL,oBAAoB,yBAAyB,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGA,eAAe,mBAAmB,SAAkB,KAA6B;AAE/E,QAAM,SAAS;AACf,QAAM,kBAAkB,IAAI,WAAW,WAAW,MAAM;AACxD,QAAM,YAAY,IAAI,WAAW,IAAI,eAAe;AAGpD,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAG/B,QAAM,cAAc,IAAI,SAAS,QAAQ,aAAa,EAAE;AACxD,MAAI,WAAW,eAAe;AAG9B,MAAI,IAAI,aAAa,SAAS,QAAQ,QAAQ,IAAI,SAAS,MAAM,aAAa;AAC5E,QAAI,WAAW;AACf,UAAMA,cAAa,IAAI,QAAQ,IAAI,SAAS,GAAG,OAAO;AACtD,WAAO,MAAM,UAAU,MAAMA,WAAU;AAAA,EACzC;AAGA,QAAM,aAAa,IAAI,QAAQ,IAAI,SAAS,GAAG;AAAA,IAC7C,QAAQ,QAAQ;AAAA,IAChB,SAAS,QAAQ;AAAA,IACjB,MAAM,QAAQ;AAAA,EAChB,CAAC;AAED,SAAO,MAAM,UAAU,MAAM,UAAU;AACzC;AA5Be;AA+Bf,eAAe,oBAAoB,SAAkB,KAA6B;AAChF,QAAM,eAAe,IAAI,aAAa,GAAG;AAEzC,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,QAAM,WAAW,IAAI,SAAS,QAAQ,cAAc,EAAE;AAGtD,QAAM,SAAS,IAAI,IAAI,QAAQ,GAAG;AAClC,SAAO,WAAW;AAElB,QAAM,aAAa,IAAI,QAAQ,OAAO,SAAS,GAAG;AAAA,IAChD,QAAQ,QAAQ;AAAA,IAChB,SAAS,QAAQ;AAAA,IACjB,MAAM,QAAQ;AAAA,EAChB,CAAC;AAED,SAAO,MAAM,aAAa,gBAAgB,UAAU;AACtD;AAjBe;AAoBf,eAAsB,UAAU,OAAuB,KAAU,KAAsC;AAErG,cAAY,QAAQ;AAGpB,UAAQ,IAAI,6BAA6B;AAC3C;AANsB;;;ACtHtB,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["newRequest"]}