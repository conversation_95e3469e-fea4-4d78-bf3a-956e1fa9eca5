{"version": 3, "sources": ["../src/api/startDevWorker/utils.ts", "../templates/startDevWorker/ProxyWorker.ts"], "mappings": ";AAAA,OAAO,YAAY;AAoBZ,SAAS,eACf,kBACqB;AACrB,MAAI,SAAS;AACb,QAAM,aAAa,IAAI,QAAW,CAAC,UAAU,YAAY;AACxD,cAAU;AACV,aAAS;AAAA,EACV,CAAC;AACD,SAAO,OAAO;AACd,SAAO,MAAM;AAIb,oBAAkB,QAAQ,UAAU;AAEpC,SAAO;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACD;AACD;AAIO,SAAS,aACf,OACA,OAAO,oBACD;AACN,QAAM,MAAM,IAAI,IAAI,IAAI;AAExB,SAAO,OAAO,KAAK,KAAK;AAExB,SAAO;AACR;;;AC7BA,IAAM,uBAAuB;AAC7B,IAAO,sBAAQ;AAAA,EACd,MAAM,KAAK,KAAK;AACf,UAAM,YAAY,IAAI,eAAe,WAAW,EAAE;AAClD,UAAM,iBAAiB,IAAI,eAAe,IAAI,SAAS;AAEvD,WAAO,eAAe,MAAM,GAAG;AAAA,EAChC;AACD;AAEO,IAAM,cAAN,MAA2C;AAAA,EACjD,YACU,OACA,KACR;AAFQ;AACA;AAAA,EACP;AAAA,EAEH;AAAA,EACA,eAAe,oBAAI,IAAwC;AAAA,EAC3D,oBAAoB,oBAAI,IAAwC;AAAA,EAEhE,MAAM,SAAkB;AACvB,QAAI,gCAAgC,OAAO,GAAG;AAG7C,aAAO,KAAK,0BAA0B,OAAO;AAAA,IAC9C;AAEA,QAAI,6BAA6B,SAAS,KAAK,GAAG,GAAG;AAGpD,aAAO,KAAK,8BAA8B,OAAO;AAAA,IAClD;AAGA,UAAM,WAAW,eAAyB;AAE1C,SAAK,aAAa,IAAI,SAAS,QAAQ;AACvC,SAAK,aAAa;AAElB,WAAO,SAAS;AAAA,EACjB;AAAA,EAEA,0BAA0B,SAAkB;AAC3C,UAAM,EAAE,GAAG,UAAU,GAAG,WAAW,IAAI,IAAI,cAAc;AACzD,UAAM,oBACL,QAAQ,QAAQ,IAAI,wBAAwB,KAAK;AAElD,SAAK,MAAM,gBAAgB,YAAY,CAAC,aAAa,CAAC;AAEtD,WAAO,IAAI,SAAS,MAAM;AAAA,MACzB,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS,EAAE,0BAA0B,kBAAkB;AAAA,IACxD,CAAC;AAAA,EACF;AAAA,EAEA,8BAA8B,SAAkB;AAC/C,UAAM,QAAQ,QAAQ,IAAI;AAC1B,YAAQ,OAAO,MAAM;AAAA,MACpB,KAAK;AACJ,aAAK,YAAY;AACjB;AAAA,MAED,KAAK;AACJ,aAAK,YAAY,MAAM;AACvB,aAAK,aAAa;AAClB,aAAK,MACH,cAAc,aAAa,EAC3B,QAAQ,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC;AAEnC;AAAA,IACF;AAEA,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,CAAC,kBAAkB;AAClB,WAAO,KAAK;AACZ,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,eAAe;AACd,UAAM,EAAE,UAAU,IAAI;AACtB,QAAI,cAAc;AAAW;AAE7B,eAAW,CAAC,SAAS,gBAAgB,KAAK,KAAK,gBAAgB,GAAG;AACjE,WAAK,kBAAkB,OAAO,OAAO;AACrC,WAAK,aAAa,OAAO,OAAO;AAEhC,YAAM,WAAW,IAAI,IAAI,QAAQ,GAAG;AACpC,YAAM,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAG3C,YAAM,gBAAgB,IAAI,IAAI,QAAQ,GAAG;AACzC,aAAO,OAAO,eAAe,UAAU,aAAa;AAGpD,YAAM,WAAW;AAAA,QAChB,UAAU,+BAA+B,CAAC;AAAA,QAC1C,QAAQ;AAAA,MACT;AACA,cAAQ,IAAI,mBAAmB,SAAS,IAAI;AAC5C,cAAQ,IAAI,2BAA2B,MAAM;AAI7C,YAAM,WAAW,QAAQ,IAAI;AAC7B,UAAI,aAAa;AAAW,gBAAQ,IAAI,mBAAmB,QAAQ;AAEnE,+BAAyB,SAAS,UAAU,QAAQ;AAGpD,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,UAAU,WAAW,CAAC,CAAC,GAAG;AACnE,YAAI,UAAU;AAAW;AAEzB,YAAI,IAAI,YAAY,MAAM,UAAU;AACnC,gBAAM,WAAW,QAAQ,QAAQ,IAAI,QAAQ,KAAK;AAClD,kBAAQ,IAAI,UAAU,GAAG,YAAY,OAAO;AAAA,QAC7C,OAAO;AACN,kBAAQ,IAAI,KAAK,KAAK;AAAA,QACvB;AAAA,MACD;AAGA,WAAK,MAAM,eAAe,IAAI,QAAQ,SAAS,EAAE,QAAQ,CAAC,CAAC,EACzD,KAAK,CAAC,QAAQ;AACd,cAAM,IAAI,SAAS,IAAI,MAAM,GAAG;AAChC,iCAAyB,IAAI,SAAS,UAAU,QAAQ;AAExD,YAAI,eAAe,GAAG,GAAG;AACxB,gBAAM,uBAAuB,SAAS,KAAK,KAAK,KAAK,SAAS;AAAA,QAC/D;AAEA,yBAAiB,QAAQ,GAAG;AAAA,MAC7B,CAAC,EACA,MAAM,CAAC,UAAiB;AAQxB,cAAM,mBACL,KAAK,aAAa,aAAa,KAAK,UAAU,aAAa;AAG5D,YAAI,cAAc,SAAS,kBAAkB,MAAM;AAClD,eAAK,6BAA6B,KAAK,KAAK;AAAA,YAC3C,MAAM;AAAA,YACN,OAAO;AAAA,cACN,MAAM,MAAM;AAAA,cACZ,SAAS,MAAM;AAAA,cACf,OAAO,MAAM;AAAA,cACb,OAAO,MAAM;AAAA,YACd;AAAA,UACD,CAAC;AAED,2BAAiB,OAAO,KAAK;AAAA,QAC9B,WAGS,QAAQ,WAAW,SAAS,QAAQ,WAAW,QAAQ;AAC/D,eAAK,kBAAkB,IAAI,SAAS,gBAAgB;AAAA,QAOrD,OAOK;AACJ,2BAAiB;AAAA,YAChB,IAAI;AAAA,cACH;AAAA,cACA;AAAA,gBACC,QAAQ;AAAA,gBACR,SAAS,EAAE,eAAe,IAAI;AAAA,cAC/B;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACH;AAAA,EACD;AACD;AAEA,SAAS,6BAA6B,KAAc,KAAmB;AACtE,SAAO,IAAI,QAAQ,IAAI,eAAe,MAAM,IAAI;AACjD;AACA,SAAS,eAAe,KAAwB;AAC/C,SAAO,IAAI,QAAQ,IAAI,cAAc,GAAG,WAAW,WAAW,KAAK;AACpE;AACA,SAAS,gCAAgC,KAAuB;AAC/D,QAAM,oBAAoB,IAAI,QAAQ,IAAI,wBAAwB;AAClE,QAAM,qBAAqB,IAAI,QAAQ,IAAI,SAAS,MAAM;AAE1D,SAAO,sBAAsB,sBAAsB;AACpD;AAEA,SAAS,6BACR,KACA,SACC;AACD,SAAO,IAAI,iBAAiB,MAAM,gBAAgB;AAAA,IACjD,QAAQ;AAAA,IACR,MAAM,KAAK,UAAU,OAAO;AAAA,EAC7B,CAAC;AACF;AAEA,SAAS,uBACR,SACA,UACA,KACA,WACC;AACD,QAAM,eAAe,IAAI,aAAa;AAGtC,MAAI,eAAe;AACnB,eAAa,GAAG,qBAAqB;AAAA,IACpC,KAAK,SAAS;AACb,sBAAgB,QAAQ;AAAA,IACzB;AAAA,EACD,CAAC;AAED,eAAa,WAAW;AAAA,IACvB,IAAI,KAAK;AACR,UACC,SAAS,WAAW,OACpB,aAAa,SAAS,uCAAuC,GAC5D;AACD,aAAK,6BAA6B,KAAK;AAAA,UACtC,MAAM;AAAA,UACN;AAAA,QACD,CAAC;AAAA,MACF;AAIA,UAAI,UAAU,YAAY;AACzB,cAAM,eAAe,IAAI,IAAI,QAAQ,GAAG;AACxC,qBAAa,WACZ,aAAa,aAAa,UAAU,QAAQ;AAE7C,YAAI,OAAO,kBAAkB,EAAE,MAAM,KAAK,CAAC;AAAA,MAC5C;AAAA,IACD;AAAA,EACD,CAAC;AAED,SAAO,aAAa,UAAU,QAAQ;AACvC;AAEA,IAAM,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0DAWiC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgB1D,SAAS,yBAAyB,SAAkB,MAAW,IAAS;AACvE,QAAM,YAAY,QAAQ,OAAO,YAAY;AAC7C,UAAQ,OAAO,YAAY;AAC3B,UAAQ,QAAQ,CAAC,OAAO,QAAQ;AAC/B,QAAI,OAAO,UAAU,YAAY,MAAM,SAAS,KAAK,IAAI,GAAG;AAC3D,cAAQ;AAAA,QACP;AAAA,QACA,MAAM,WAAW,KAAK,QAAQ,GAAG,MAAM,EAAE,WAAW,KAAK,MAAM,GAAG,IAAI;AAAA,MACvE;AAAA,IACD;AAAA,EACD,CAAC;AACD,aAAW,UAAU,WAAW;AAC/B,YAAQ;AAAA,MACP;AAAA,MACA,OAAO;AAAA,QACN,IAAI,OAAO,UAAU,KAAK,iBAAiB;AAAA,QAC3C,UAAU,GAAG;AAAA,MACd;AAAA,IACD;AAAA,EACD;AACD;", "names": []}