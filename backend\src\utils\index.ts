import { ApiResponse } from '../types';

// 生成UUID
export function generateId(): string {
  return crypto.randomUUID();
}

// 生成随机昵称
export function generateRandomNickname(): string {
  const adjectives = [
    '快乐的', '聪明的', '勇敢的', '友善的', '神秘的', '活泼的', '冷静的', '幽默的',
    '优雅的', '坚强的', '温柔的', '机智的', '热情的', '安静的', '开朗的', '谦逊的'
  ];
  
  const nouns = [
    '狐狸', '熊猫', '老虎', '狮子', '大象', '海豚', '企鹅', '猫咪',
    '小狗', '兔子', '松鼠', '猴子', '鹦鹉', '蝴蝶', '独角兽', '龙'
  ];
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  const number = Math.floor(Math.random() * 9999) + 1;
  
  return `${adjective}${noun}${number}`;
}

// 创建成功响应
export function createSuccessResponse<T>(data: T, message?: string): ApiResponse<T> {
  return {
    success: true,
    data,
    message
  };
}

// 创建错误响应
export function createErrorResponse(error: string, message?: string): ApiResponse {
  return {
    success: false,
    error,
    message
  };
}

// 创建JSON响应
export function createJsonResponse<T>(data: ApiResponse<T>, status: number = 200): Response {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

// 处理CORS预检请求
export function handleCorsOptions(): Response {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}

// 解析请求体
export async function parseRequestBody<T>(request: Request): Promise<T | null> {
  try {
    const contentType = request.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      return await request.json();
    }
    return null;
  } catch (error) {
    console.error('Failed to parse request body:', error);
    return null;
  }
}

// 验证必需字段
export function validateRequiredFields(data: any, fields: string[]): string | null {
  for (const field of fields) {
    if (!data || data[field] === undefined || data[field] === null) {
      return `Missing required field: ${field}`;
    }
  }
  return null;
}

// 格式化时间戳
export function formatTimestamp(date: Date = new Date()): string {
  return date.toISOString();
}

// 清理过期连接
export function isConnectionExpired(lastActivity: string, timeoutMinutes: number = 30): boolean {
  const lastActivityTime = new Date(lastActivity).getTime();
  const now = Date.now();
  const timeoutMs = timeoutMinutes * 60 * 1000;
  return (now - lastActivityTime) > timeoutMs;
}

// 广播消息到所有连接
export function broadcastToConnections(connections: Map<string, WebSocket>, message: any, excludeId?: string): void {
  const messageStr = JSON.stringify(message);
  
  for (const [connectionId, ws] of connections.entries()) {
    if (excludeId && connectionId === excludeId) {
      continue;
    }
    
    try {
      if (ws.readyState === WebSocket.READY_STATE_OPEN) {
        ws.send(messageStr);
      } else {
        // 清理已关闭的连接
        connections.delete(connectionId);
      }
    } catch (error) {
      console.error(`Failed to send message to connection ${connectionId}:`, error);
      connections.delete(connectionId);
    }
  }
}

// 创建WebSocket响应
export function createWebSocketResponse(webSocket: WebSocket): Response {
  return new Response(null, {
    status: 101,
    webSocket,
  });
}

// 错误处理包装器
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R | Response> {
  return async (...args: T): Promise<R | Response> => {
    try {
      return await fn(...args);
    } catch (error) {
      console.error('Handler error:', error);
      return createJsonResponse(
        createErrorResponse('Internal server error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  };
}

// 限流工具
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(
    private maxRequests: number = 100,
    private windowMs: number = 60000 // 1分钟
  ) {}
  
  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    // 获取或创建请求记录
    let requests = this.requests.get(identifier) || [];
    
    // 清理过期请求
    requests = requests.filter(timestamp => timestamp > windowStart);
    
    // 检查是否超过限制
    if (requests.length >= this.maxRequests) {
      return false;
    }
    
    // 添加当前请求
    requests.push(now);
    this.requests.set(identifier, requests);
    
    return true;
  }
  
  // 清理过期数据
  cleanup(): void {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    for (const [identifier, requests] of this.requests.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > windowStart);
      if (validRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, validRequests);
      }
    }
  }
}
