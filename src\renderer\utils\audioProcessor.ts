export class AudioProcessor {
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private gainNode: GainNode | null = null;
  private stream: MediaStream | null = null;
  private dataArray: Uint8Array | null = null;
  private isProcessing = false;
  
  // 语音活动检测参数
  private voiceThreshold = 30; // 语音检测阈值
  private silenceThreshold = 10; // 静音检测阈值
  private voiceActivityCallback?: (isActive: boolean, volume: number) => void;
  private lastVoiceActivity = false;
  private voiceActivityTimeout: NodeJS.Timeout | null = null;

  async initialize(stream: MediaStream): Promise<void> {
    try {
      this.stream = stream;
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // 创建音频节点
      this.microphone = this.audioContext.createMediaStreamSource(stream);
      this.analyser = this.audioContext.createAnalyser();
      this.gainNode = this.audioContext.createGain();
      
      // 配置分析器
      this.analyser.fftSize = 256;
      this.analyser.smoothingTimeConstant = 0.8;
      
      // 连接音频节点
      this.microphone.connect(this.analyser);
      this.microphone.connect(this.gainNode);
      
      // 创建数据数组
      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);
      
      console.log('Audio processor initialized');
    } catch (error) {
      console.error('Failed to initialize audio processor:', error);
      throw error;
    }
  }

  startProcessing(): void {
    if (!this.analyser || !this.dataArray || this.isProcessing) {
      return;
    }
    
    this.isProcessing = true;
    this.processAudio();
  }

  stopProcessing(): void {
    this.isProcessing = false;
    if (this.voiceActivityTimeout) {
      clearTimeout(this.voiceActivityTimeout);
      this.voiceActivityTimeout = null;
    }
  }

  private processAudio(): void {
    if (!this.isProcessing || !this.analyser || !this.dataArray) {
      return;
    }

    // 获取频域数据
    this.analyser.getByteFrequencyData(this.dataArray);
    
    // 计算音量（RMS）
    let sum = 0;
    for (let i = 0; i < this.dataArray.length; i++) {
      sum += this.dataArray[i] * this.dataArray[i];
    }
    const rms = Math.sqrt(sum / this.dataArray.length);
    const volume = Math.min(100, Math.max(0, (rms / 128) * 100));
    
    // 语音活动检测
    this.detectVoiceActivity(volume);
    
    // 继续处理
    requestAnimationFrame(() => this.processAudio());
  }

  private detectVoiceActivity(volume: number): void {
    const isVoiceActive = volume > this.voiceThreshold;
    
    if (isVoiceActive !== this.lastVoiceActivity) {
      // 状态改变时，添加一个小延迟以避免频繁切换
      if (this.voiceActivityTimeout) {
        clearTimeout(this.voiceActivityTimeout);
      }
      
      this.voiceActivityTimeout = setTimeout(() => {
        this.lastVoiceActivity = isVoiceActive;
        this.voiceActivityCallback?.(isVoiceActive, volume);
      }, isVoiceActive ? 50 : 200); // 检测到语音时快速响应，静音时稍慢
    } else if (isVoiceActive) {
      // 如果仍在说话，更新音量
      this.voiceActivityCallback?.(true, volume);
    }
  }

  setVoiceActivityCallback(callback: (isActive: boolean, volume: number) => void): void {
    this.voiceActivityCallback = callback;
  }

  setVolume(volume: number): void {
    if (this.gainNode) {
      // 将0-100的音量转换为0-1的增益
      const gain = Math.max(0, Math.min(1, volume / 100));
      this.gainNode.gain.setValueAtTime(gain, this.audioContext!.currentTime);
    }
  }

  setMuted(muted: boolean): void {
    if (this.stream) {
      this.stream.getAudioTracks().forEach(track => {
        track.enabled = !muted;
      });
    }
  }

  setVoiceThreshold(threshold: number): void {
    this.voiceThreshold = Math.max(0, Math.min(100, threshold));
  }

  setSilenceThreshold(threshold: number): void {
    this.silenceThreshold = Math.max(0, Math.min(100, threshold));
  }

  // 获取当前音量级别
  getCurrentVolume(): number {
    if (!this.analyser || !this.dataArray) {
      return 0;
    }

    this.analyser.getByteFrequencyData(this.dataArray);
    
    let sum = 0;
    for (let i = 0; i < this.dataArray.length; i++) {
      sum += this.dataArray[i] * this.dataArray[i];
    }
    const rms = Math.sqrt(sum / this.dataArray.length);
    return Math.min(100, Math.max(0, (rms / 128) * 100));
  }

  // 获取频谱数据（用于可视化）
  getFrequencyData(): Uint8Array | null {
    if (!this.analyser || !this.dataArray) {
      return null;
    }
    
    this.analyser.getByteFrequencyData(this.dataArray);
    return new Uint8Array(this.dataArray);
  }

  // 应用音频滤波器
  applyNoiseReduction(enabled: boolean): void {
    // 这里可以实现噪音抑制算法
    // 由于Web Audio API的限制，这里只是一个占位符
    console.log('Noise reduction:', enabled ? 'enabled' : 'disabled');
  }

  // 应用回声消除
  applyEchoCancellation(enabled: boolean): void {
    // 回声消除通常在getUserMedia时配置
    console.log('Echo cancellation:', enabled ? 'enabled' : 'disabled');
  }

  // 应用自动增益控制
  applyAutoGainControl(enabled: boolean): void {
    // 自动增益控制通常在getUserMedia时配置
    console.log('Auto gain control:', enabled ? 'enabled' : 'disabled');
  }

  // 清理资源
  dispose(): void {
    this.stopProcessing();
    
    if (this.microphone) {
      this.microphone.disconnect();
      this.microphone = null;
    }
    
    if (this.analyser) {
      this.analyser.disconnect();
      this.analyser = null;
    }
    
    if (this.gainNode) {
      this.gainNode.disconnect();
      this.gainNode = null;
    }
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.stream = null;
    this.dataArray = null;
    this.voiceActivityCallback = undefined;
    
    if (this.voiceActivityTimeout) {
      clearTimeout(this.voiceActivityTimeout);
      this.voiceActivityTimeout = null;
    }
  }
}

// 音频设备管理
export class AudioDeviceManager {
  private devices: MediaDeviceInfo[] = [];

  async getDevices(): Promise<{
    audioInputs: MediaDeviceInfo[];
    audioOutputs: MediaDeviceInfo[];
  }> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.devices = devices;
      
      return {
        audioInputs: devices.filter(device => device.kind === 'audioinput'),
        audioOutputs: devices.filter(device => device.kind === 'audiooutput'),
      };
    } catch (error) {
      console.error('Failed to enumerate devices:', error);
      return { audioInputs: [], audioOutputs: [] };
    }
  }

  async requestPermissions(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Failed to request audio permissions:', error);
      return false;
    }
  }

  async createStream(deviceId?: string): Promise<MediaStream> {
    const constraints: MediaStreamConstraints = {
      audio: {
        deviceId: deviceId ? { exact: deviceId } : undefined,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 48000,
        channelCount: 1,
      },
      video: false,
    };

    return await navigator.mediaDevices.getUserMedia(constraints);
  }

  // 设置音频输出设备（如果浏览器支持）
  async setAudioOutput(element: HTMLAudioElement, deviceId: string): Promise<boolean> {
    try {
      if ('setSinkId' in element) {
        await (element as any).setSinkId(deviceId);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to set audio output device:', error);
      return false;
    }
  }
}
