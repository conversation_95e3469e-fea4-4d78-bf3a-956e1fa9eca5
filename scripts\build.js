const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建 OPZ Voice Chat...');

// 检查必要的文件
const requiredFiles = [
  'src/main/main.ts',
  'src/renderer/index.tsx',
  'webpack.config.js',
  'package.json'
];

console.log('📋 检查必要文件...');
for (const file of requiredFiles) {
  if (!fs.existsSync(file)) {
    console.error(`❌ 缺少必要文件: ${file}`);
    process.exit(1);
  }
}
console.log('✅ 所有必要文件都存在');

// 清理旧的构建文件
console.log('🧹 清理旧的构建文件...');
if (fs.existsSync('dist')) {
  fs.rmSync('dist', { recursive: true, force: true });
}
if (fs.existsSync('release')) {
  fs.rmSync('release', { recursive: true, force: true });
}

try {
  // 构建应用
  console.log('🔨 构建应用...');
  execSync('npm run build', { stdio: 'inherit' });
  
  // 检查构建结果
  if (!fs.existsSync('dist/main.js') || !fs.existsSync('dist/index.html')) {
    throw new Error('构建失败：缺少必要的输出文件');
  }
  
  console.log('✅ 应用构建完成');
  
  // 打包应用
  console.log('📦 打包应用...');
  execSync('npm run dist:win', { stdio: 'inherit' });
  
  console.log('🎉 构建完成！');
  console.log('📁 输出目录: release/');
  
  // 显示输出文件
  if (fs.existsSync('release')) {
    const files = fs.readdirSync('release');
    console.log('📄 生成的文件:');
    files.forEach(file => {
      const filePath = path.join('release', file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024 / 1024).toFixed(2);
      console.log(`  - ${file} (${size} MB)`);
    });
  }
  
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
