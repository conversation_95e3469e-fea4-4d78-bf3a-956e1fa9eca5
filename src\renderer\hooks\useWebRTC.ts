import { useState, useEffect, useRef, useCallback } from 'react';
import { User, VoiceState } from '../types';
import { AudioProcessor, AudioDeviceManager } from '../utils/audioProcessor';

interface WebRTCConfig {
  iceServers: RTCIceServer[];
}

interface UseWebRTCProps {
  user: User | null;
  voiceState: VoiceState;
  onVoiceStateChange: (newState: Partial<VoiceState>) => void;
}

interface PeerConnection {
  id: string;
  userId: string;
  connection: RTCPeerConnection;
  stream?: MediaStream;
}

export function useWebRTC({ user, voiceState, onVoiceStateChange }: UseWebRTCProps) {
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [peers, setPeers] = useState<Map<string, PeerConnection>>(new Map());
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentVolume, setCurrentVolume] = useState(0);

  const wsRef = useRef<WebSocket | null>(null);
  const localStreamRef = useRef<MediaStream | null>(null);
  const audioProcessorRef = useRef<AudioProcessor | null>(null);
  const deviceManagerRef = useRef<AudioDeviceManager>(new AudioDeviceManager());
  
  const webrtcConfig: WebRTCConfig = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
    ]
  };

  // 初始化本地媒体流
  const initializeLocalStream = useCallback(async () => {
    try {
      // 使用设备管理器创建流
      const stream = await deviceManagerRef.current.createStream();

      setLocalStream(stream);
      localStreamRef.current = stream;
      setError(null);

      // 初始化音频处理器
      if (!audioProcessorRef.current) {
        audioProcessorRef.current = new AudioProcessor();
        await audioProcessorRef.current.initialize(stream);

        // 设置语音活动检测回调
        audioProcessorRef.current.setVoiceActivityCallback((isActive, volume) => {
          setIsSpeaking(isActive);
          setCurrentVolume(volume);

          // 更新用户状态
          if (user && user.isSpeaking !== isActive) {
            onVoiceStateChange({
              // 这里可以添加更多状态更新
            });
          }
        });

        audioProcessorRef.current.startProcessing();
      }

      // 设置音量控制
      const audioTrack = stream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !voiceState.isMuted;
      }

      return stream;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to access microphone';
      setError(errorMessage);
      console.error('Failed to get user media:', err);
      throw err;
    }
  }, [voiceState.isMuted, user?.isSpeaking, onVoiceStateChange]);

  // 创建WebSocket连接
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      // 这里应该连接到你的Cloudflare Workers后端
      const wsUrl = 'ws://localhost:8787/api/room/ws'; // 开发环境
      // const wsUrl = 'wss://your-worker.your-subdomain.workers.dev/api/room/ws'; // 生产环境
      
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('WebSocket connected');
        wsRef.current = ws;
        
        // 发送加入房间消息
        if (user) {
          ws.send(JSON.stringify({
            type: 'JOIN_ROOM',
            payload: { user },
            timestamp: new Date().toISOString(),
            userId: user.id,
          }));
        }
      };
      
      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };
      
      ws.onclose = () => {
        console.log('WebSocket disconnected');
        wsRef.current = null;
        // 尝试重连
        setTimeout(connectWebSocket, 3000);
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setError('WebSocket connection failed');
      };
      
    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError('Failed to connect to server');
    }
  }, [user]);

  // 处理WebSocket消息
  const handleWebSocketMessage = useCallback(async (message: any) => {
    switch (message.type) {
      case 'ROOM_EVENT':
        await handleRoomEvent(message.payload);
        break;
      case 'SIGNALING':
        await handleSignalingMessage(message.payload);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }, []);

  // 处理房间事件
  const handleRoomEvent = useCallback(async (event: any) => {
    switch (event.type) {
      case 'USER_JOIN':
        await createPeerConnection(event.payload.id, event.payload);
        break;
      case 'USER_LEAVE':
        removePeerConnection(event.payload.userId);
        break;
      case 'USER_UPDATE':
        // 处理用户状态更新
        break;
    }
  }, []);

  // 处理WebRTC信令消息
  const handleSignalingMessage = useCallback(async (signaling: any) => {
    const { from, type, payload } = signaling;

    if (!user || from === user.id) return; // 忽略自己的消息
    
    const peer = peers.get(from);
    if (!peer) return;
    
    try {
      switch (type) {
        case 'offer':
          await peer.connection.setRemoteDescription(payload);
          const answer = await peer.connection.createAnswer();
          await peer.connection.setLocalDescription(answer);
          sendSignalingMessage(from, 'answer', answer);
          break;
          
        case 'answer':
          await peer.connection.setRemoteDescription(payload);
          break;
          
        case 'ice-candidate':
          await peer.connection.addIceCandidate(payload);
          break;
      }
    } catch (err) {
      console.error('Signaling error:', err);
    }
  }, [peers, user]);

  // 创建对等连接
  const createPeerConnection = useCallback(async (peerId: string, peerUser: User) => {
    if (!user || peers.has(peerId) || peerId === user.id) return;
    
    try {
      const connection = new RTCPeerConnection(webrtcConfig);
      
      // 添加本地流
      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach(track => {
          connection.addTrack(track, localStreamRef.current!);
        });
      }
      
      // 处理远程流
      connection.ontrack = (event) => {
        const [remoteStream] = event.streams;
        setPeers(prev => {
          const updated = new Map(prev);
          const peer = updated.get(peerId);
          if (peer) {
            peer.stream = remoteStream;
            updated.set(peerId, peer);
          }
          return updated;
        });
      };
      
      // 处理ICE候选
      connection.onicecandidate = (event) => {
        if (event.candidate) {
          sendSignalingMessage(peerId, 'ice-candidate', event.candidate);
        }
      };
      
      // 连接状态变化
      connection.onconnectionstatechange = () => {
        console.log(`Peer ${peerId} connection state:`, connection.connectionState);
        if (connection.connectionState === 'failed') {
          removePeerConnection(peerId);
        }
      };
      
      const peerConnection: PeerConnection = {
        id: peerId,
        userId: peerUser.id,
        connection,
      };
      
      setPeers(prev => new Map(prev).set(peerId, peerConnection));
      
      // 创建offer
      const offer = await connection.createOffer();
      await connection.setLocalDescription(offer);
      sendSignalingMessage(peerId, 'offer', offer);
      
    } catch (err) {
      console.error('Failed to create peer connection:', err);
    }
  }, [peers, user, webrtcConfig]);

  // 移除对等连接
  const removePeerConnection = useCallback((peerId: string) => {
    const peer = peers.get(peerId);
    if (peer) {
      peer.connection.close();
      setPeers(prev => {
        const updated = new Map(prev);
        updated.delete(peerId);
        return updated;
      });
    }
  }, [peers]);

  // 发送信令消息
  const sendSignalingMessage = useCallback((to: string, type: string, payload: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN && user) {
      wsRef.current.send(JSON.stringify({
        type: 'SIGNALING',
        payload: {
          type,
          payload,
          from: user.id,
          to,
          channelId: voiceState.currentChannelId,
        },
        timestamp: new Date().toISOString(),
        userId: user.id,
      }));
    }
  }, [user, voiceState.currentChannelId]);

  // 连接到语音频道
  const connectToVoiceChannel = useCallback(async (channelId: string) => {
    try {
      if (!localStreamRef.current) {
        await initializeLocalStream();
      }

      onVoiceStateChange({
        isConnected: true,
        currentChannelId: channelId,
      });

      // 发送加入频道消息
      if (wsRef.current?.readyState === WebSocket.OPEN && user) {
        wsRef.current.send(JSON.stringify({
          type: 'JOIN_CHANNEL',
          payload: { userId: user.id, channelId },
          timestamp: new Date().toISOString(),
          userId: user.id,
        }));
      }

    } catch (err) {
      console.error('Failed to connect to voice channel:', err);
      setError('Failed to connect to voice channel');
    }
  }, [user, initializeLocalStream, onVoiceStateChange]);

  // 断开语音频道连接
  const disconnectFromVoiceChannel = useCallback(() => {
    // 关闭所有对等连接
    peers.forEach((peer) => {
      peer.connection.close();
    });
    setPeers(new Map());
    
    // 发送离开频道消息
    if (wsRef.current?.readyState === WebSocket.OPEN && user) {
      wsRef.current.send(JSON.stringify({
        type: 'LEAVE_CHANNEL',
        payload: { userId: user.id, channelId: voiceState.currentChannelId },
        timestamp: new Date().toISOString(),
        userId: user.id,
      }));
    }
    
    onVoiceStateChange({
      isConnected: false,
      currentChannelId: undefined,
    });
  }, [peers, user, voiceState.currentChannelId, onVoiceStateChange]);

  // 切换静音状态
  const toggleMute = useCallback(() => {
    const newMutedState = !voiceState.isMuted;

    if (audioProcessorRef.current) {
      audioProcessorRef.current.setMuted(newMutedState);
    }

    if (localStreamRef.current) {
      const audioTrack = localStreamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !newMutedState;
      }
    }

    onVoiceStateChange({ isMuted: newMutedState });
  }, [voiceState.isMuted, onVoiceStateChange]);

  // 设置音量
  const setVolume = useCallback((volume: number) => {
    if (audioProcessorRef.current) {
      audioProcessorRef.current.setVolume(volume);
    }
    onVoiceStateChange({ volume });
  }, [onVoiceStateChange]);

  // 切换关闭声音状态
  const toggleDeafen = useCallback(() => {
    const newDeafenedState = !voiceState.isDeafened;

    // 关闭声音时也会自动静音
    if (newDeafenedState) {
      if (audioProcessorRef.current) {
        audioProcessorRef.current.setMuted(true);
      }
      if (localStreamRef.current) {
        const audioTrack = localStreamRef.current.getAudioTracks()[0];
        if (audioTrack) {
          audioTrack.enabled = false;
        }
      }
      onVoiceStateChange({ isDeafened: true, isMuted: true });
    } else {
      onVoiceStateChange({ isDeafened: false });
    }
  }, [voiceState.isDeafened, onVoiceStateChange]);

  // 初始化
  useEffect(() => {
    if (!isInitialized) {
      connectWebSocket();
      setIsInitialized(true);
    }
    
    return () => {
      // 清理资源
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach(track => track.stop());
      }
      if (audioProcessorRef.current) {
        audioProcessorRef.current.dispose();
        audioProcessorRef.current = null;
      }
      peers.forEach(peer => peer.connection.close());
    };
  }, [isInitialized, connectWebSocket, peers]);

  // 监听静音状态变化
  useEffect(() => {
    if (localStreamRef.current) {
      const audioTrack = localStreamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !voiceState.isMuted;
      }
    }
  }, [voiceState.isMuted]);

  return {
    localStream,
    peers: Array.from(peers.values()),
    isInitialized,
    error,
    isSpeaking,
    currentVolume,
    connectToVoiceChannel,
    disconnectFromVoiceChannel,
    toggleMute,
    toggleDeafen,
    setVolume,
    deviceManager: deviceManagerRef.current,
  };
}
