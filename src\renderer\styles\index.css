@tailwind base;
@tailwind components;
@tailwind utilities;

/* Discord风格的自定义样式 */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  body {
    @apply bg-discord-gray-900 text-gray-100 font-discord;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
}

@layer components {
  /* 按钮样式 */
  .btn-primary {
    @apply bg-discord-blurple hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-discord-gray-700 hover:bg-discord-gray-600 text-gray-100 font-medium py-2 px-4 rounded transition-colors duration-200;
  }
  
  .btn-danger {
    @apply bg-discord-red hover:bg-red-600 text-white font-medium py-2 px-4 rounded transition-colors duration-200;
  }
  
  /* 输入框样式 */
  .input-primary {
    @apply bg-discord-gray-800 border border-discord-gray-700 text-gray-100 rounded px-3 py-2 focus:outline-none focus:border-discord-blurple transition-colors duration-200;
  }
  
  /* 频道样式 */
  .channel-item {
    @apply flex items-center px-2 py-1 mx-2 rounded hover:bg-discord-gray-800 cursor-pointer transition-colors duration-150;
  }
  
  .channel-item.active {
    @apply bg-discord-gray-700;
  }
  
  /* 用户样式 */
  .user-item {
    @apply flex items-center px-2 py-1 mx-2 rounded hover:bg-discord-gray-800 cursor-pointer transition-colors duration-150;
  }
  
  /* 自定义滚动条 */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #202225 #2f3136;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #2f3136;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #202225;
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #1a1c1f;
  }
}

@layer utilities {
  .drag-region {
    -webkit-app-region: drag;
  }
  
  .no-drag {
    -webkit-app-region: no-drag;
  }
}
